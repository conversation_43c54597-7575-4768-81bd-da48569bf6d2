<?php

namespace App\Services;

use App\Config\AppConfig;
use App\Models\ResignRecord;
use App\Models\Certificate;
use App\Services\ZsignService;
use App\Services\CertificateService;
use App\Utils\IpaParser;

class ResignService
{
    private AppConfig $config;
    private ResignRecord $resignRecordModel;
    private Certificate $certificateModel;
    private ZsignService $zsignService;
    private CertificateService $certificateService;
    private string $resignStoragePath;
    private string $tempPath;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
        $this->resignRecordModel = new ResignRecord();
        $this->certificateModel = new Certificate();
        $this->zsignService = new ZsignService();
        $this->certificateService = new CertificateService();

        // 设置存储路径
        $basePath = $this->config->get('app_storage_path') ?? '/data/storage/';
        $this->resignStoragePath = $basePath . '/resigned_ipas';
        $this->tempPath = $basePath . '/temp';

        // 确保目录存在
        $this->ensureDirectoriesExist();
    }

    /**
     * 提交IPA重签任务
     */
    public function submitResignTask(array $fileData, array $resignConfig, string $userId): array
    {
        try {
            // 验证文件
            $validation = $this->validateIpaFile($fileData);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'error' => $validation['error']
                ];
            }

            // 保存原始IPA文件到临时目录
            $tempFilename = 'original_' . uniqid() . '.ipa';
            $tempFilePath = $this->tempPath . '/' . $tempFilename;

            // 复制文件到临时目录
            if (!copy($fileData['tmp_name'], $tempFilePath)) {
                return [
                    'success' => false,
                    'error' => 'IPA文件保存失败'
                ];
            }

            // 解析IPA文件信息
            $ipaInfo = IpaParser::parseInfoPlist($tempFilePath);

            // 创建重签记录（先创建以获取ID）
            $recordData = [
                'user_id' => $userId,
                'original_ipa_path' => $tempFilePath,
                'original_filename' => $fileData['name'],
                'bundle_id' => $ipaInfo['bundle_id'] ?? null,
                'app_name' => $ipaInfo['app_name'] ?? null,
                'version' => $ipaInfo['version'] ?? null,
                'build' => $ipaInfo['build'] ?? null,
                'original_file_size' => filesize($tempFilePath),
                'new_bundle_id' => $resignConfig['new_bundle_id'] ?? null,
                'new_app_name' => $resignConfig['new_app_name'] ?? null,
                'resign_options' => $resignConfig['options'] ?? [],
                'distribution_enabled' => $resignConfig['distribution_enabled'] ?? false,
                'certificate_pair_id' => $resignConfig['certificate_pair_id'] ?? null
            ];

            $recordId = $this->resignRecordModel->create($recordData);

            // 提取应用图标（失败不影响主流程）
            try {
                $iconUrl = \App\Utils\IpaParser::extractAppIcon($tempFilePath, (string)$recordId, 'resign');
                if ($iconUrl) {
                    // 更新记录添加图标URL
                    $this->resignRecordModel->updateStatus((string)$recordId, 'pending', ['icon_url' => $iconUrl]);
                }
            } catch (\Exception $e) {
                error_log("图标提取失败，但不影响重签任务: " . $e->getMessage());
            }

            // 如果启用了自动处理，立即开始重签
            if ($resignConfig['auto_process'] ?? true) {
                $this->processResignTask((string)$recordId);
            }

            return [
                'success' => true,
                'record_id' => (string)$recordId,
                'message' => '重签任务提交成功'
            ];
        } catch (\Exception $e) {
            error_log("重签任务提交失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => '重签任务提交失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理重签任务
     */
    public function processResignTask(string $recordId): array
    {
        try {
            $record = $this->resignRecordModel->findById($recordId);
            if (!$record) {
                return [
                    'success' => false,
                    'error' => '重签记录不存在'
                ];
            }

            if ($record['status'] !== 'pending') {
                return [
                    'success' => false,
                    'error' => '任务状态不正确'
                ];
            }

            // 更新状态为处理中
            $this->resignRecordModel->updateStatus($recordId, 'processing');

            // 获取证书对
            $certificatePair = $this->getCertificatePair($record);
            if (!$certificatePair) {
                $this->resignRecordModel->updateStatus($recordId, 'failed', [
                    'error_message' => '没有可用的证书对'
                ]);
                return [
                    'success' => false,
                    'error' => '没有可用的证书对'
                ];
            }

            // 执行重签
            $resignResult = $this->performResign($record, $certificatePair);

            if ($resignResult['success']) {
                // 更新重签结果
                $this->resignRecordModel->updateResignResult($recordId, $resignResult);

                // 自动生成分发链接（所有重签成功的任务都生成）
                $distributionResult = $this->generateDistributionLinks($recordId, $resignResult);
                if ($distributionResult['success']) {
                    $this->resignRecordModel->updateDistributionInfo($recordId, $distributionResult);
                }

                return [
                    'success' => true,
                    'message' => '重签完成',
                    'resigned_ipa_path' => $resignResult['resigned_ipa_path']
                ];
            } else {
                // 更新失败状态
                $this->resignRecordModel->updateStatus($recordId, 'failed', [
                    'error_message' => $resignResult['error'],
                    'error_details' => $resignResult['details'] ?? null
                ]);

                return $resignResult;
            }
        } catch (\Exception $e) {
            error_log("重签任务处理失败: " . $e->getMessage());

            // 更新失败状态
            $this->resignRecordModel->updateStatus($recordId, 'failed', [
                'error_message' => '重签处理异常: ' . $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => '重签处理异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 执行重签操作
     */
    private function performResign(array $record, array $certificatePair): array
    {
        try {
            // 生成输出文件名
            $timestamp = date('Y-m-d_H-i-s');
            $outputFilename = pathinfo($record['original_filename'], PATHINFO_FILENAME) . '_resigned_' . $timestamp . '.ipa';
            $outputPath = $this->resignStoragePath . '/' . $outputFilename;

            // 获取证书文件路径
            $p12Path = $certificatePair['p12']['file_path'];
            $provisionPath = $certificatePair['mobileprovision']['file_path'];

            // 获取证书密码
            $p12Certificate = $this->certificateModel->findById($certificatePair['p12']['_id']);
            if (!$p12Certificate) {
                return [
                    'success' => false,
                    'error' => '无法获取P12证书信息'
                ];
            }

            // 构建重签选项
            $resignOptions = [
                'bundle_id' => $record['new_bundle_id'],
                'bundle_name' => $record['new_app_name'],
                'force' => true
            ];

            // 添加密码（如果可用）
            if (isset($certificatePair['p12']['password'])) {
                $resignOptions['password'] = $certificatePair['p12']['password'];
            } else {
                // 临时解决方案：尝试常见密码
                $commonPasswords = ['123456', 'password', 'test', 'testpass123', ''];
                $p12Path = $certificatePair['p12']['file_path'];

                foreach ($commonPasswords as $testPassword) {
                    $testCommand = 'openssl pkcs12 -info -in ' . escapeshellarg($p12Path) .
                        ' -passin pass:' . escapeshellarg($testPassword) . ' -noout -legacy 2>&1';

                    $testOutput = shell_exec($testCommand);
                    $testSuccess = (strpos($testOutput, 'MAC verified OK') !== false ||
                        strpos($testOutput, 'Certificate') !== false);

                    if ($testSuccess) {
                        $resignOptions['password'] = $testPassword;
                        break;
                    }
                }

                // 如果仍然没有找到密码，返回错误
                if (!isset($resignOptions['password'])) {
                    return [
                        'success' => false,
                        'error' => 'P12证书密码未知，请重新上传证书并提供正确密码'
                    ];
                }
            }

            // 执行重签
            $result = $this->zsignService->resignIpa(
                $record['original_ipa_path'],
                $p12Path,
                $provisionPath,
                $outputPath,
                $resignOptions
            );

            if ($result['success']) {
                return [
                    'success' => true,
                    'resigned_ipa_path' => $outputPath,
                    'resigned_filename' => $outputFilename,
                    'resigned_file_size' => filesize($outputPath)
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'],
                    'details' => $result['details'] ?? null
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => '重签执行异常: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取证书对
     */
    private function getCertificatePair(array $record): ?array
    {
        // 如果指定了证书对ID，使用指定的证书对
        if (!empty($record['certificate_pair_id'])) {
            $pairs = $this->certificateModel->getAvailableCertificatePairs();
            foreach ($pairs as $pair) {
                if ($pair['id'] === $record['certificate_pair_id']) {
                    return $pair;
                }
            }
        }

        // 否则自动选择最佳证书对
        return $this->certificateService->getBestCertificatePair($record['bundle_id']);
    }

    /**
     * 生成分发链接
     */
    private function generateDistributionLinks(string $recordId, array $resignResult): array
    {
        try {
            $distributionService = new \App\Services\DistributionService();
            return $distributionService->createDistribution($recordId);
        } catch (\Exception $e) {
            error_log("生成分发链接失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => '生成分发链接失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证IPA文件
     */
    private function validateIpaFile(array $fileData): array
    {
        if ($fileData['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => '文件上传失败'
            ];
        }

        $extension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));
        if ($extension !== 'ipa') {
            return [
                'valid' => false,
                'error' => '只支持.ipa格式的文件'
            ];
        }

        if ($fileData['size'] > 2 * 1024 * 1024 * 1024) { // 2GB限制
            return [
                'valid' => false,
                'error' => 'IPA文件大小不能超过2GB'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 确保存储目录存在
     */
    private function ensureDirectoriesExist(): void
    {
        $directories = [$this->resignStoragePath, $this->tempPath];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * 获取用户的重签记录
     */
    public function getUserResignRecords(string $userId, int $limit = 20, int $skip = 0): array
    {
        return $this->resignRecordModel->getUserRecords($userId, $limit, $skip);
    }

    /**
     * 获取重签记录详情
     */
    public function getResignRecordDetail(string $recordId): ?array
    {
        return $this->resignRecordModel->findById($recordId);
    }

    /**
     * 处理待处理的重签任务队列
     */
    public function processResignQueue(): void
    {
        $pendingRecords = $this->resignRecordModel->getPendingRecords(5); // 一次处理5个

        foreach ($pendingRecords as $record) {
            $this->processResignTask($record['_id']);
        }
    }

    /**
     * 删除重签记录
     */
    public function deleteResignRecord(string $recordId, string $userId, string $userRole = 'user'): array
    {
        try {
            // 获取记录详情
            $record = $this->resignRecordModel->findById($recordId);
            if (!$record) {
                return [
                    'success' => false,
                    'error' => '重签记录不存在'
                ];
            }

            // 权限检查：管理员可以删除所有记录，普通用户只能删除自己的记录
            if ($userRole !== 'admin' && $record['user_id'] !== $userId) {
                return [
                    'success' => false,
                    'error' => '无权删除此记录'
                ];
            }

            // 删除相关文件
            $this->cleanupRecordFiles($record);

            // 从数据库删除记录
            $deleted = $this->resignRecordModel->delete($recordId);
            if (!$deleted) {
                return [
                    'success' => false,
                    'error' => '删除记录失败'
                ];
            }

            return [
                'success' => true,
                'message' => '重签记录删除成功'
            ];
        } catch (\Exception $e) {
            error_log("删除重签记录失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => '删除记录失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 清理记录相关的文件
     */
    private function cleanupRecordFiles(array $record): void
    {
        try {
            // 删除原始IPA文件
            if (!empty($record['original_ipa_path']) && file_exists($record['original_ipa_path'])) {
                @unlink($record['original_ipa_path']);
                error_log("删除原始IPA文件: " . $record['original_ipa_path']);
            }

            // 删除重签后的IPA文件
            if (!empty($record['resigned_ipa_path']) && file_exists($record['resigned_ipa_path'])) {
                @unlink($record['resigned_ipa_path']);
                error_log("删除重签IPA文件: " . $record['resigned_ipa_path']);
            }

            // 删除应用图标文件
            if (!empty($record['icon_url'])) {
                $iconPath = $this->getIconPathFromUrl($record['icon_url']);
                if ($iconPath && file_exists($iconPath)) {
                    @unlink($iconPath);
                    error_log("删除应用图标: " . $iconPath);
                }
            }

            // 删除分发相关文件（如果有的话）
            $this->cleanupDistributionFiles($record);
        } catch (\Exception $e) {
            error_log("清理记录文件时出错: " . $e->getMessage());
        }
    }

    /**
     * 从图标URL获取文件路径
     */
    private function getIconPathFromUrl(string $iconUrl): ?string
    {
        // 图标URL格式: /storage/img/icons/resign_recordId.png
        if (strpos($iconUrl, '/storage/img/icons/') === 0) {
            $filename = basename($iconUrl);
            return dirname(__DIR__, 2) . '/storage/img/icons/' . $filename;
        }
        return null;
    }

    /**
     * 清理分发相关文件
     */
    private function cleanupDistributionFiles(array $record): void
    {
        try {
            // 如果有plist文件，删除它
            if (!empty($record['plist_url'])) {
                $plistPath = $this->getPlistPathFromUrl($record['plist_url']);
                if ($plistPath && file_exists($plistPath)) {
                    @unlink($plistPath);
                    error_log("删除plist文件: " . $plistPath);
                }
            }

            // 如果有二维码图片，删除它
            if (!empty($record['qr_code_url'])) {
                $qrPath = $this->getQrCodePathFromUrl($record['qr_code_url']);
                if ($qrPath && file_exists($qrPath)) {
                    @unlink($qrPath);
                    error_log("删除二维码文件: " . $qrPath);
                }
            }
        } catch (\Exception $e) {
            error_log("清理分发文件时出错: " . $e->getMessage());
        }
    }

    /**
     * 从plist URL获取文件路径
     */
    private function getPlistPathFromUrl(string $plistUrl): ?string
    {
        // 根据实际的plist存储路径调整
        if (strpos($plistUrl, '/storage/plists/') === 0) {
            $filename = basename($plistUrl);
            return dirname(__DIR__, 2) . '/storage/plists/' . $filename;
        }
        return null;
    }

    /**
     * 从二维码URL获取文件路径
     */
    private function getQrCodePathFromUrl(string $qrUrl): ?string
    {
        // 根据实际的二维码存储路径调整
        if (strpos($qrUrl, '/storage/qrcodes/') === 0) {
            $filename = basename($qrUrl);
            return dirname(__DIR__, 2) . '/storage/qrcodes/' . $filename;
        }
        return null;
    }

    /**
     * 批量删除过期的重签记录
     */
    public function cleanupExpiredRecords(int $daysOld = 30): array
    {
        try {
            $cutoffDate = new \MongoDB\BSON\UTCDateTime((time() - ($daysOld * 24 * 60 * 60)) * 1000);

            // 获取过期记录
            $expiredRecords = $this->resignRecordModel->getExpiredRecords($cutoffDate);
            $cleanedCount = 0;

            foreach ($expiredRecords as $record) {
                // 清理文件
                $this->cleanupRecordFiles($record);

                // 删除记录
                if ($this->resignRecordModel->delete($record['_id'])) {
                    $cleanedCount++;
                }
            }

            return [
                'success' => true,
                'cleaned_count' => $cleanedCount,
                'message' => "清理了 {$cleanedCount} 个过期的重签记录"
            ];
        } catch (\Exception $e) {
            error_log("清理过期记录失败: " . $e->getMessage());
            return [
                'success' => false,
                'error' => '清理过期记录失败: ' . $e->getMessage()
            ];
        }
    }
}

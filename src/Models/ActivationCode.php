<?php

namespace App\Models;

use App\Database\MongoDB;
use MongoDB\BSON\ObjectId;
use MongoDB\Collection;

class ActivationCode
{
    private Collection $collection;
    private MongoDB $db;

    public function __construct()
    {
        $this->db = MongoDB::getInstance();
        $this->collection = $this->db->getCollection('activation_codes');
    }

    public function create(array $data): ObjectId
    {
        $codeData = [
            'code' => $data['code'],
            'created_by' => new ObjectId($data['created_by']),
            'used_by' => null,
            'expires_at' => new \MongoDB\BSON\UTCDateTime(strtotime($data['expires_at']) * 1000),
            'used_at' => null,
            'status' => 'active'
        ];

        $result = $this->collection->insertOne($codeData);
        return $result->getInsertedId();
    }

    public function findByCode(string $code): ?array
    {
        $activationCode = $this->collection->findOne(['code' => $code]);
        return $activationCode ? $this->documentToArray($activationCode) : null;
    }

    public function findById(string $id): ?array
    {
        $activationCode = $this->collection->findOne(['_id' => new ObjectId($id)]);
        return $activationCode ? $this->documentToArray($activationCode) : null;
    }

    public function useCode(string $codeId, string $userId): bool
    {
        $result = $this->collection->updateOne(
            ['_id' => new ObjectId($codeId)],
            [
                '$set' => [
                    'used_by' => new ObjectId($userId),
                    'used_at' => new \MongoDB\BSON\UTCDateTime(),
                    'status' => 'used'
                ],
                '$inc' => [
                    'used_count' => 1
                ]
            ]
        );
        return $result->getModifiedCount() > 0;
    }

    public function isExpired(array $activationCode): bool
    {
        // 激活码本身不过期，只有在被使用后才失效
        return false;
    }

    public function isUsed(array $activationCode): bool
    {
        // 兼容两种数据格式
        if (isset($activationCode['status'])) {
            return $activationCode['status'] === 'used';
        }
        return $activationCode['is_used'] ?? false;
    }

    public function isValid(array $activationCode): bool
    {
        return !$this->isExpired($activationCode) && !$this->isUsed($activationCode);
    }

    public function getAllCodes(int $limit = 50, int $skip = 0): array
    {
        $cursor = $this->collection->find(
            [],
            [
                'limit' => $limit,
                'skip' => $skip,
                'sort' => ['created_at' => -1]
            ]
        );

        $codes = [];
        foreach ($cursor as $code) {
            $codes[] = $this->documentToArray($code);
        }

        return $codes;
    }

    public function getActiveCodes(): array
    {
        $cursor = $this->collection->find(['status' => 'active']);

        $codes = [];
        foreach ($cursor as $code) {
            $codes[] = $this->documentToArray($code);
        }

        return $codes;
    }

    public function deleteCode(string $codeId): bool
    {
        $result = $this->collection->deleteOne(['_id' => new ObjectId($codeId)]);
        return $result->getDeletedCount() > 0;
    }

    public function generateCode(): string
    {
        // 生成8位随机激活码，包含字母和数字
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $code = '';
        for ($i = 0; $i < 8; $i++) {
            $code .= $chars[rand(0, strlen($chars) - 1)];
        }
        return $code;
    }

    public function countCodes(array $filter = []): int
    {
        return $this->collection->countDocuments($filter);
    }

    public function createCode(int $expiresIn = 24, int $maxUses = 1, ?int $expiresInDays = null): string
    {
        $code = strtoupper(bin2hex(random_bytes(4))); // 生成8位随机码

        $codeData = [
            'code' => $code,
            'created_by' => null, // 系统创建
            'used_by' => null,
            'used_at' => null,
            'max_uses' => $maxUses,
            'used_count' => 0,
            'user_validity_days' => $expiresInDays ?? ($expiresIn / 24), // 用户激活后的有效天数
            'expires_at' => null, // 激活码本身不过期
            'created_at' => new \MongoDB\BSON\UTCDateTime(),
            'status' => 'active'
        ];

        $this->collection->insertOne($codeData);
        return $code;
    }

    public function countActiveCodes(): int
    {
        return $this->collection->countDocuments(['status' => 'active']);
    }

    private function documentToArray($document): array
    {
        $data = $document->getArrayCopy();

        // 转换ObjectId为字符串
        if (isset($data['_id']) && $data['_id'] instanceof ObjectId) {
            $data['_id'] = (string) $data['_id'];
        }

        if (isset($data['created_by']) && $data['created_by'] instanceof ObjectId) {
            $data['created_by'] = (string) $data['created_by'];
        }

        if (isset($data['used_by']) && $data['used_by'] instanceof ObjectId) {
            $data['used_by'] = (string) $data['used_by'];
        }

        // 转换日期为ISO字符串
        if (isset($data['created_at']) && $data['created_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['created_at'] = $data['created_at']->toDateTime()->format('c');
        }

        if (isset($data['expires_at']) && $data['expires_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['expires_at'] = $data['expires_at']->toDateTime()->format('c');
        }

        if (isset($data['used_at']) && $data['used_at'] instanceof \MongoDB\BSON\UTCDateTime) {
            $data['used_at'] = $data['used_at']->toDateTime()->format('c');
        }

        return $data;
    }
}

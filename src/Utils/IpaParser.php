<?php

namespace App\Utils;

use App\Config\AppConfig;

class IpaParser
{
    /**
     * 从IPA文件中提取应用图标
     */
    public static function extractAppIcon(string $ipaPath, string $recordId, string $prefix = ''): ?string
    {
        try {
            // 获取配置中的图标存储路径
            $config = AppConfig::getInstance();
            $iconDir = $config->get('icon_storage_path') ?? '/data/storage/uploads/icons';

            // 确保图标目录存在
            if (!is_dir($iconDir)) {
                // 尝试创建目录，包括父目录
                $parentDir = dirname($iconDir);
                if (!is_dir($parentDir)) {
                    mkdir($parentDir, 0755, true);
                }

                if (!mkdir($iconDir, 0755, true)) {
                    // 如果创建失败，尝试使用系统临时目录
                    $iconDir = sys_get_temp_dir() . '/xios_icons';
                    if (!is_dir($iconDir)) {
                        mkdir($iconDir, 0755, true);
                    }
                }
            }

            // 确保目录可写
            if (!is_writable($iconDir)) {
                chmod($iconDir, 0755);
            }

            // 创建临时目录
            $tempDir = sys_get_temp_dir() . '/ipa_extract_' . uniqid();
            mkdir($tempDir, 0755, true);

            try {
                // 提取IPA文件中的应用包
                $extractCmd = "unzip -q " . escapeshellarg($ipaPath) . " -d " . escapeshellarg($tempDir);
                shell_exec($extractCmd . " 2>&1");

                // 查找.app目录
                $payloadDir = $tempDir . '/Payload';
                if (!is_dir($payloadDir)) {
                    throw new \Exception("无法找到Payload目录");
                }

                $appDirs = glob($payloadDir . '/*.app');
                if (empty($appDirs)) {
                    throw new \Exception("无法找到.app目录");
                }

                $appDir = $appDirs[0];

                // 查找图标文件（按优先级排序）
                $iconPatterns = [
                    '<EMAIL>',    // iPhone 6 Plus/6s Plus/7 Plus/8 Plus
                    '<EMAIL>',    // iPhone 4s/5/5s/6/6s/7/8
                    '<EMAIL>',    // iPad
                    '<EMAIL>',    // iPhone 6 Plus/6s Plus/7 Plus/8 Plus (Spotlight)
                    '<EMAIL>',    // iPhone/iPad (Spotlight)
                    '<EMAIL>',    // iPhone 6 Plus/6s Plus/7 Plus/8 Plus (Settings)
                    '<EMAIL>',    // iPhone/iPad (Settings)
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    'Icon.png'
                ];

                $iconFile = null;
                foreach ($iconPatterns as $pattern) {
                    $iconPath = $appDir . '/' . $pattern;
                    if (file_exists($iconPath)) {
                        $iconFile = $iconPath;
                        break;
                    }
                }

                // 如果没找到预定义的图标，尝试查找任何PNG图标文件
                if (!$iconFile) {
                    $iconFiles = glob($appDir . '/*Icon*.png');
                    if (!empty($iconFiles)) {
                        // 选择文件大小最大的图标（通常质量更好）
                        usort($iconFiles, function ($a, $b) {
                            return filesize($b) - filesize($a);
                        });
                        $iconFile = $iconFiles[0];
                    }
                }

                if (!$iconFile || !file_exists($iconFile)) {
                    throw new \Exception("无法找到应用图标文件");
                }

                // 复制图标到目标目录
                $iconFilename = ($prefix ? $prefix . '_' : '') . $recordId . '.png';
                $targetPath = $iconDir . '/' . $iconFilename;

                if (!copy($iconFile, $targetPath)) {
                    error_log("图标复制失败: 从 {$iconFile} 到 {$targetPath}");
                    throw new \Exception("无法复制图标文件");
                }

                // 返回图标的访问URL
                if (strpos($iconDir, sys_get_temp_dir()) === 0) {
                    // 如果使用临时目录，返回null（不提供图标）
                    return null;
                } else {
                    // 使用API域名返回图标URL
                    $apiUrl = $config->get('api_url') ?? 'https://api.ios.xxyx.cn';
                    return $apiUrl . '/storage/uploads/icons/' . $iconFilename;
                }
            } finally {
                // 清理临时目录
                self::removeDirectory($tempDir);
            }
        } catch (\Exception $e) {
            error_log("图标提取失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 递归删除目录
     */
    private static function removeDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                self::removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }

    public static function parseInfoPlist(string $ipaPath): array
    {
        $tempPlist = sys_get_temp_dir() . "/temp_" . uniqid() . ".plist";

        try {
            if (!file_exists($ipaPath)) {
                throw new \Exception("IPA文件不存在: {$ipaPath}");
            }

            // 提取Info.plist文件
            $extractCmd = "unzip -p " . escapeshellarg($ipaPath) . " \"Payload/*.app/Info.plist\" > " . escapeshellarg($tempPlist);
            shell_exec($extractCmd . " 2>&1");

            if (!file_exists($tempPlist) || filesize($tempPlist) == 0) {
                throw new \Exception("无法提取Info.plist文件");
            }

            // 尝试使用plistutil转换为XML
            $plistData = null;
            if (self::commandExists("plistutil")) {
                $tempXmlPlist = $tempPlist . ".xml";
                $convertCmd = "plistutil -i " . escapeshellarg($tempPlist) . " -o " . escapeshellarg($tempXmlPlist) . " -f xml";
                $output = shell_exec($convertCmd . " 2>&1");

                if (file_exists($tempXmlPlist)) {
                    $xmlContent = file_get_contents($tempXmlPlist);
                    $plistData = self::parseXmlPlist($xmlContent);
                    unlink($tempXmlPlist); // 清理临时XML文件
                }
            }

            // 尝试使用plutil (macOS)
            if (!$plistData && self::commandExists("plutil")) {
                $convertCmd = "plutil -convert json " . escapeshellarg($tempPlist) . " -o -";
                $jsonOutput = shell_exec($convertCmd . " 2>&1");

                if ($jsonOutput && $jsonData = json_decode($jsonOutput, true)) {
                    $plistData = $jsonData;
                }
            }

            if (!$plistData) {
                throw new \Exception("无法解析plist文件，需要安装plistutil或plutil工具");
            }

            // 提取关键信息
            $bundleId = $plistData["CFBundleIdentifier"] ?? "unknown.bundle.id";
            $appName = $plistData["CFBundleDisplayName"]
                ?? $plistData["CFBundleName"]
                ?? pathinfo($ipaPath, PATHINFO_FILENAME);
            $version = $plistData["CFBundleShortVersionString"] ?? "1.0.0";
            $build = $plistData["CFBundleVersion"] ?? "1";



            return [
                "bundle_id" => $bundleId,
                "app_name" => $appName,
                "version" => $version,
                "build" => $build,
                "minimum_os_version" => $plistData["MinimumOSVersion"] ?? "Unknown",
                "supported_devices" => $plistData["UIDeviceFamily"] ?? [],
                "required_capabilities" => $plistData["UIRequiredDeviceCapabilities"] ?? [],
                "raw_plist_data" => $plistData
            ];
        } catch (\Exception $e) {
            throw new \Exception("解析IPA文件失败: " . $e->getMessage());
        } finally {
            if (file_exists($tempPlist)) {
                unlink($tempPlist);
            }
        }
    }

    public static function isValidIpa(string $ipaPath): bool
    {
        try {
            $zipArchive = new \ZipArchive();
            if ($zipArchive->open($ipaPath) !== TRUE) {
                return false;
            }

            $hasPayload = false;
            for ($i = 0; $i < $zipArchive->numFiles; $i++) {
                $filename = $zipArchive->getNameIndex($i);
                if (strpos($filename, "Payload/") === 0 && strpos($filename, ".app/") !== false) {
                    $hasPayload = true;
                    break;
                }
            }

            $zipArchive->close();
            return $hasPayload;
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function getIpaBasicInfo(string $ipaPath): array
    {
        $info = [
            "file_size" => filesize($ipaPath),
            "file_name" => basename($ipaPath),
            "is_valid" => false,
            "app_name" => null,
            "error" => null
        ];

        try {
            $zipArchive = new \ZipArchive();
            if ($zipArchive->open($ipaPath) !== TRUE) {
                $info["error"] = "无法打开IPA文件";
                return $info;
            }

            for ($i = 0; $i < $zipArchive->numFiles; $i++) {
                $filename = $zipArchive->getNameIndex($i);
                if (preg_match("/Payload\/([^\/]+\.app)\//", $filename, $matches)) {
                    $info["is_valid"] = true;
                    $info["app_name"] = str_replace(".app", "", $matches[1]);
                    break;
                }
            }

            $zipArchive->close();
        } catch (\Exception $e) {
            $info["error"] = $e->getMessage();
        }

        return $info;
    }

    private static function commandExists(string $command): bool
    {
        $result = shell_exec("which $command 2>/dev/null");
        return !empty($result);
    }

    /**
     * 解析XML格式的plist文件
     */
    private static function parseXmlPlist(string $xmlContent): ?array
    {
        try {
            $xml = simplexml_load_string($xmlContent);
            if ($xml === false) {
                return null;
            }

            // 查找dict元素
            $dict = $xml->dict;
            if (!$dict) {
                return null;
            }

            return self::parsePlistDict($dict);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 解析plist字典
     */
    private static function parsePlistDict($dict): array
    {
        $result = [];
        $keys = $dict->key;
        $values = [];

        // 收集所有值元素
        foreach ($dict->children() as $child) {
            if ($child->getName() !== "key") {
                $values[] = $child;
            }
        }

        // 配对键值
        for ($i = 0; $i < count($keys) && $i < count($values); $i++) {
            $key = (string)$keys[$i];
            $value = self::parsePlistValue($values[$i]);
            $result[$key] = $value;
        }

        return $result;
    }

    /**
     * 解析plist值
     */
    private static function parsePlistValue($element)
    {
        switch ($element->getName()) {
            case "string":
                return (string)$element;
            case "integer":
                return (int)$element;
            case "real":
                return (float)$element;
            case "true":
                return true;
            case "false":
                return false;
            case "array":
                $result = [];
                foreach ($element->children() as $child) {
                    $result[] = self::parsePlistValue($child);
                }
                return $result;
            case "dict":
                return self::parsePlistDict($element);
            default:
                return (string)$element;
        }
    }
}

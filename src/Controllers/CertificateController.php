<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\CsrService;
use App\Services\CertificateService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class CertificateController
{
    private AuthService $authService;
    private CsrService $csrService;
    private CertificateService $certificateService;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->csrService = new CsrService();
        $this->certificateService = new CertificateService();
    }

    /**
     * 生成CSR文件
     */
    public function generateCsr(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $data = json_decode($request->getContent(), true);
        if (!$data) {
            return new JsonResponse([
                'success' => false,
                'error' => '无效的请求数据'
            ], 400);
        }

        $result = $this->csrService->generateCsr($data, $user['_id']);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 下载CSR文件
     */
    public function downloadCsr(Request $request): Response
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $certificateId = $request->attributes->get('certificateId');
        $result = $this->csrService->downloadCsr($certificateId);

        if (!$result['success']) {
            return new JsonResponse($result, 404);
        }

        $response = new Response($result['content']);
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $result['filename'] . '"');
        $response->headers->set('Content-Length', strlen($result['content']));

        return $response;
    }

    /**
     * 获取用户的CSR列表
     */
    public function getUserCsrList(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $csrList = $this->csrService->getUserCsrList($user['_id']);

        return new JsonResponse([
            'success' => true,
            'csr_list' => $csrList
        ]);
    }

    /**
     * 上传P12证书（管理员）
     */
    public function uploadP12Certificate(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $files = $request->files->all();
        if (!isset($files['certificate'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '未找到证书文件'
            ], 400);
        }

        $certificateInfo = [
            'name' => $request->request->get('name'),
            'password' => $request->request->get('password'),
            'certificate_type' => $request->request->get('certificate_type', 'distribution')
        ];

        if (empty($certificateInfo['name']) || empty($certificateInfo['password'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '证书名称和密码不能为空'
            ], 400);
        }

        $fileData = [
            'name' => $files['certificate']->getClientOriginalName(),
            'tmp_name' => $files['certificate']->getPathname(),
            'size' => $files['certificate']->getSize(),
            'error' => $files['certificate']->getError()
        ];

        $result = $this->certificateService->uploadP12Certificate($fileData, $certificateInfo, $user['_id']);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 上传mobileprovision文件（管理员）
     */
    public function uploadMobileProvision(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $files = $request->files->all();
        if (!isset($files['provision'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '未找到mobileprovision文件'
            ], 400);
        }

        $fileData = [
            'name' => $files['provision']->getClientOriginalName(),
            'tmp_name' => $files['provision']->getPathname(),
            'size' => $files['provision']->getSize(),
            'error' => $files['provision']->getError()
        ];

        $result = $this->certificateService->uploadMobileProvision($fileData, $user['_id']);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取所有证书列表（管理员）
     */
    public function getAllCertificates(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $certificates = $this->certificateService->getAllCertificates();

        return new JsonResponse([
            'success' => true,
            'certificates' => $certificates
        ]);
    }

    /**
     * 获取可用的证书对
     */
    public function getAvailableCertificatePairs(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $certificateModel = new \App\Models\Certificate();
        $pairs = $certificateModel->getAvailableCertificatePairs();

        return new JsonResponse([
            'success' => true,
            'certificate_pairs' => $pairs
        ]);
    }

    /**
     * 删除证书（管理员）
     */
    public function deleteCertificate(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $certificateId = $request->attributes->get('certificateId');
        $result = $this->certificateService->deleteCertificate($certificateId);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取证书详情
     */
    public function getCertificateDetail(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $certificateId = $request->attributes->get('certificateId');
        $certificateModel = new \App\Models\Certificate();
        $certificate = $certificateModel->findById($certificateId);

        if (!$certificate) {
            return new JsonResponse([
                'success' => false,
                'error' => '证书不存在'
            ], 404);
        }

        // 普通用户只能查看自己的证书
        if ($user['role'] !== 'admin' && $certificate['uploaded_by'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        return new JsonResponse([
            'success' => true,
            'certificate' => $certificate
        ]);
    }

    /**
     * 获取认证用户信息
     */
    private function getAuthenticatedUser(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return null;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        return $this->authService->verifyToken($token);
    }
}

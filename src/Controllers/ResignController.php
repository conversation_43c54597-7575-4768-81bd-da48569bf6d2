<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\ResignService;
use App\Models\ResignRecord;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class ResignController
{
    private AuthService $authService;
    private ResignService $resignService;
    private ResignRecord $resignRecordModel;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->resignService = new ResignService();
        $this->resignRecordModel = new ResignRecord();
    }

    /**
     * 提交IPA重签任务
     */
    public function submitResignTask(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $files = $request->files->all();
        if (!isset($files['ipa'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '未找到IPA文件'
            ], 400);
        }

        // 获取重签配置
        $resignConfig = [
            'new_bundle_id' => $request->request->get('new_bundle_id'),
            'new_app_name' => $request->request->get('new_app_name'),
            'certificate_pair_id' => $request->request->get('certificate_pair_id'),
            'distribution_enabled' => true, // 所有重签都启用分发
            'auto_process' => $request->request->getBoolean('auto_process', true),
            'options' => [
                'force' => $request->request->getBoolean('force', true)
            ]
        ];

        $fileData = [
            'name' => $files['ipa']->getClientOriginalName(),
            'tmp_name' => $files['ipa']->getPathname(),
            'size' => $files['ipa']->getSize(),
            'error' => $files['ipa']->getError()
        ];

        $result = $this->resignService->submitResignTask($fileData, $resignConfig, $user['_id']);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取用户的重签记录
     */
    public function getUserResignRecords(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $userId = $user['_id'];
        $role = $user['role'] ?? 'user';

        // 获取分页参数，与上传中心保持一致
        $page = max(1, (int) $request->query->get('page', 1));
        $pageSize = max(1, min(100, (int) $request->query->get('page_size', 10))); // 限制最大100条

        if ($role === 'admin') {
            // 管理员查所有记录（支持分页）
            $result = $this->resignRecordModel->getAllRecordsWithUserPaginated($page, $pageSize);
        } else {
            // 普通用户查自己的记录（支持分页）
            $result = $this->resignRecordModel->findByUserIdPaginated($userId, $page, $pageSize);
        }

        return new JsonResponse([
            'success' => true,
            'records' => $result['records'],
            'pagination' => $result['pagination']
        ]);
    }

    /**
     * 获取重签记录详情
     */
    public function getResignRecordDetail(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $recordId = $request->attributes->get('recordId');
        $record = $this->resignService->getResignRecordDetail($recordId);

        if (!$record) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签记录不存在'
            ], 404);
        }

        // 检查权限（普通用户只能查看自己的记录）
        if ($user['role'] !== 'admin' && $record['user_id'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        return new JsonResponse([
            'success' => true,
            'record' => $record
        ]);
    }

    /**
     * 下载重签后的IPA文件
     */
    public function downloadResignedIpa(Request $request): Response
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $recordId = $request->attributes->get('recordId');
        $record = $this->resignService->getResignRecordDetail($recordId);

        if (!$record) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签记录不存在'
            ], 404);
        }

        // 检查权限
        if ($user['role'] !== 'admin' && $record['user_id'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        if ($record['status'] !== 'success' || !$record['resigned_ipa_path']) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签未完成或文件不存在'
            ], 400);
        }

        if (!file_exists($record['resigned_ipa_path'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签文件不存在'
            ], 404);
        }

        // 更新下载统计
        $this->resignRecordModel->updateDownloadStats($recordId);

        // 返回文件
        $response = new Response(file_get_contents($record['resigned_ipa_path']));
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $record['resigned_filename'] . '"');
        $response->headers->set('Content-Length', (string)$record['resigned_file_size']);

        return $response;
    }

    /**
     * 重新处理失败的重签任务
     */
    public function retryResignTask(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $recordId = $request->attributes->get('recordId');
        $record = $this->resignService->getResignRecordDetail($recordId);

        if (!$record) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签记录不存在'
            ], 404);
        }

        // 检查权限
        if ($user['role'] !== 'admin' && $record['user_id'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        if ($record['status'] !== 'failed') {
            return new JsonResponse([
                'success' => false,
                'error' => '只能重试失败的任务'
            ], 400);
        }

        // 重置状态为pending
        $this->resignRecordModel->updateStatus($recordId, 'pending', [
            'error_message' => null,
            'error_details' => null
        ]);

        // 重新处理任务
        $result = $this->resignService->processResignTask($recordId);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取所有重签记录（管理员）
     * 注意：这个方法现在已经被 getUserResignRecords 统一处理，保留是为了向后兼容
     */
    public function getAllResignRecords(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        // 获取分页参数，与上传中心保持一致
        $page = max(1, (int) $request->query->get('page', 1));
        $pageSize = max(1, min(100, (int) $request->query->get('page_size', 50))); // 管理员默认50条

        $result = $this->resignRecordModel->getAllRecordsWithUserPaginated($page, $pageSize);

        return new JsonResponse([
            'success' => true,
            'records' => $result['records'],
            'pagination' => $result['pagination']
        ]);
    }

    /**
     * 删除重签记录
     */
    public function deleteResignRecord(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $recordId = $request->attributes->get('recordId');
        $userId = $user['_id'];
        $role = $user['role'] ?? 'user';

        // 使用服务层的删除方法，包含完整的文件清理和权限检查
        $result = $this->resignService->deleteResignRecord($recordId, $userId, $role);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 获取认证用户信息
     */
    private function getAuthenticatedUser(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return null;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        return $this->authService->verifyToken($token);
    }
}

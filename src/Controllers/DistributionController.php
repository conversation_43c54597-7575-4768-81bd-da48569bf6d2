<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Services\DistributionService;
use App\Models\DistributionRecord;
use App\Models\ResignRecord;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class DistributionController
{
    private AuthService $authService;
    private DistributionService $distributionService;
    private DistributionRecord $distributionModel;
    private ResignRecord $resignRecordModel;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->distributionService = new DistributionService();
        $this->distributionModel = new DistributionRecord();
        $this->resignRecordModel = new ResignRecord();
    }

    /**
     * 创建分发链接
     */
    public function createDistribution(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $data = json_decode($request->getContent(), true);
        if (!$data || !isset($data['resign_record_id'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少重签记录ID'
            ], 400);
        }

        $resignRecordId = $data['resign_record_id'];
        $resignRecord = $this->resignRecordModel->findById($resignRecordId);

        if (!$resignRecord) {
            return new JsonResponse([
                'success' => false,
                'error' => '重签记录不存在'
            ], 404);
        }

        // 检查权限
        if ($user['role'] !== 'admin' && $resignRecord['user_id'] !== $user['_id']) {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $distributionConfig = [
            'title' => $data['title'] ?? null,
            'subtitle' => $data['subtitle'] ?? null,
            'description' => $data['description'] ?? null,
            'minimum_os_version' => $data['minimum_os_version'] ?? '10.0',
            'expires_in_days' => $data['expires_in_days'] ?? null
        ];

        $result = $this->distributionService->createDistribution($resignRecordId, $distributionConfig);

        return new JsonResponse($result, $result['success'] ? 200 : 400);
    }

    /**
     * 安装页面（显示安装按钮和应用信息）
     */
    public function installPage(Request $request): Response
    {
        $distributionId = $request->attributes->get('distributionId');
        $distribution = $this->distributionService->getDistribution($distributionId);

        if (!$distribution || $distribution['status'] !== 'active') {
            return new Response('分发链接不存在或已失效', 404);
        }

        // 记录查看统计
        $this->distributionService->recordView($distributionId);

        // 检查是否过期
        if ($distribution['expires_at'] && strtotime($distribution['expires_at']) < time()) {
            return new Response('分发链接已过期', 410);
        }

        // 生成安装页面HTML
        $html = $this->generateInstallPageHtml($distribution);

        return new Response($html, 200, [
            'Content-Type' => 'text/html; charset=utf-8'
        ]);
    }

    /**
     * 获取plist文件
     */
    public function getPlist(Request $request): Response
    {
        $distributionId = $request->attributes->get('distributionId');
        $distribution = $this->distributionService->getDistribution($distributionId);

        if (!$distribution || $distribution['status'] !== 'active') {
            return new JsonResponse(['error' => '分发链接不存在或已失效'], 404);
        }

        // 检查是否过期
        if ($distribution['expires_at'] && strtotime($distribution['expires_at']) < time()) {
            return new JsonResponse(['error' => '分发链接已过期'], 410);
        }

        $plistResult = $this->distributionService->getPlistContent($distributionId);

        if (!$plistResult['success']) {
            return new JsonResponse(['error' => $plistResult['error']], 404);
        }

        return new Response($plistResult['content'], 200, [
            'Content-Type' => 'application/x-plist',
            'Content-Disposition' => 'attachment; filename="manifest.plist"'
        ]);
    }

    /**
     * 下载IPA文件
     */
    public function downloadIpa(Request $request): Response
    {
        $distributionId = $request->attributes->get('distributionId');
        $distribution = $this->distributionService->getDistribution($distributionId);

        if (!$distribution || $distribution['status'] !== 'active') {
            return new JsonResponse(['error' => '分发链接不存在或已失效'], 404);
        }

        // 检查是否过期
        if ($distribution['expires_at'] && strtotime($distribution['expires_at']) < time()) {
            return new JsonResponse(['error' => '分发链接已过期'], 410);
        }

        // 获取重签记录
        $resignRecord = $this->resignRecordModel->findById($distribution['resign_record_id']);
        if (!$resignRecord || !$resignRecord['resigned_ipa_path']) {
            return new JsonResponse(['error' => 'IPA文件不存在'], 404);
        }

        if (!file_exists($resignRecord['resigned_ipa_path'])) {
            return new JsonResponse(['error' => 'IPA文件不存在'], 404);
        }

        // 记录安装统计
        $metadata = [
            'user_agent' => $request->headers->get('User-Agent'),
            'ip' => $request->getClientIp()
        ];
        $this->distributionService->recordInstall($distributionId, $metadata);

        // 返回文件
        $response = new Response(file_get_contents($resignRecord['resigned_ipa_path']));
        $response->headers->set('Content-Type', 'application/octet-stream');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $distribution['app_name'] . '.ipa"');
        $response->headers->set('Content-Length', (string)$resignRecord['resigned_file_size']);

        return $response;
    }



    /**
     * 获取用户的分发记录
     */
    public function getUserDistributions(Request $request): JsonResponse
    {
        // 验证用户权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user) {
            return new JsonResponse([
                'success' => false,
                'error' => '未授权访问'
            ], 401);
        }

        $limit = (int)$request->query->get('limit', 20);
        $skip = (int)$request->query->get('skip', 0);

        $distributions = $this->distributionService->getUserDistributions($user['_id'], $limit, $skip);
        $totalCount = $this->distributionModel->countUserRecords($user['_id']);

        return new JsonResponse([
            'success' => true,
            'distributions' => $distributions,
            'total_count' => $totalCount,
            'limit' => $limit,
            'skip' => $skip
        ]);
    }

    /**
     * 获取分发统计（管理员）
     */
    public function getDistributionStats(Request $request): JsonResponse
    {
        // 验证管理员权限
        $user = $this->getAuthenticatedUser($request);
        if (!$user || $user['role'] !== 'admin') {
            return new JsonResponse([
                'success' => false,
                'error' => '权限不足'
            ], 403);
        }

        $stats = $this->distributionService->getDistributionStats();
        $popularApps = $this->distributionService->getPopularApps(10);

        return new JsonResponse([
            'success' => true,
            'stats' => $stats,
            'popular_apps' => $popularApps
        ]);
    }

    /**
     * 生成安装页面HTML
     */
    private function generateInstallPageHtml(array $distribution): string
    {
        $appName = htmlspecialchars($distribution['app_name']);
        $subtitle = htmlspecialchars($distribution['subtitle'] ?? '');
        $description = htmlspecialchars($distribution['description'] ?? '');
        $version = htmlspecialchars($distribution['version'] ?? '');
        $installUrl = "itms-services://?action=download-manifest&url=" . urlencode($distribution['plist_url']);

        return <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$appName} - 应用安装</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }
        .app-icon {
            width: 80px;
            height: 80px;
            border-radius: 16px;
            margin: 0 auto 20px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
        }
        .app-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        .app-subtitle {
            color: #666;
            margin-bottom: 16px;
        }
        .app-version {
            color: #999;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .install-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 20px;
            text-decoration: none;
            display: inline-block;
            box-sizing: border-box;
        }
        .install-btn:hover {
            background: #0056CC;
        }
        .description {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-top: 20px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-top: 20px;
            font-size: 12px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-icon">📱</div>
        <div class="app-name">{$appName}</div>
        <div class="app-subtitle">{$subtitle}</div>
        <div class="app-version">版本 {$version}</div>
        
        <a href="{$installUrl}" class="install-btn">安装应用</a>
        
        <div class="description">{$description}</div>
        
        <div class="warning">
            ⚠️ 请确保您的设备已信任企业证书，否则应用可能无法正常运行。
        </div>
    </div>
</body>
</html>
HTML;
    }



    /**
     * 获取认证用户信息
     */
    private function getAuthenticatedUser(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');

        if (!$token) {
            return null;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        return $this->authService->verifyToken($token);
    }
}

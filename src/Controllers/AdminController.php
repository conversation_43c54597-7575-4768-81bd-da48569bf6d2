<?php

namespace App\Controllers;

use App\Services\AuthService;
use App\Models\User;
use App\Models\ActivationCode;
use App\Models\GitHubAccount;
use App\Models\UploadRecord;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class AdminController
{
    private $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    // 创建用户
    public function createUser(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $data = json_decode($request->getContent(), true);

            // 验证必填字段
            $required = ['username', 'email', 'password'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return new JsonResponse(['error' => "字段 {$field} 不能为空"], 400);
                }
            }

            // 验证邮箱格式
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return new JsonResponse(['error' => '邮箱格式不正确'], 400);
            }

            // 检查用户名和邮箱是否已存在
            $userModel = new User();
            if ($userModel->findByUsername($data['username'])) {
                return new JsonResponse(['error' => '用户名已存在'], 400);
            }

            if ($userModel->findByEmail($data['email'])) {
                return new JsonResponse(['error' => '邮箱已存在'], 400);
            }

            // 创建用户
            $userId = $userModel->create([
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => $data['password'],
                'role' => $data['role'] ?? 'user',
                'status' => $data['status'] ?? 'inactive'
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => '用户创建成功',
                'user_id' => $userId
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '创建用户失败: ' . $e->getMessage()], 500);
        }
    }

    // 获取所有用户
    public function getUsers(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $userModel = new User();
            $users = $userModel->getAllUsers();

            return new JsonResponse([
                'success' => true,
                'users' => $users
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '获取用户列表失败: ' . $e->getMessage()], 500);
        }
    }

    // 创建激活码
    public function createActivationCode(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $data = json_decode($request->getContent(), true);

            $expiresInDays = $data['expires_in_days'] ?? 30; // 用户激活后的有效天数
            $maxUses = $data['max_uses'] ?? 1; // 默认使用1次
            $expiresIn = $expiresInDays * 24; // 转换为小时（兼容旧接口）

            $activationCodeModel = new ActivationCode();
            $code = $activationCodeModel->createCode($expiresIn, $maxUses, $expiresInDays);

            return new JsonResponse([
                'success' => true,
                'message' => '激活码创建成功',
                'code' => $code
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '创建激活码失败: ' . $e->getMessage()], 500);
        }
    }

    // 获取激活码列表
    public function getActivationCodes(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $activationCodeModel = new ActivationCode();
            $codes = $activationCodeModel->getAllCodes();

            return new JsonResponse([
                'success' => true,
                'codes' => $codes
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '获取激活码列表失败: ' . $e->getMessage()], 500);
        }
    }

    // 添加GitHub账号
    public function addGitHubAccount(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $data = json_decode($request->getContent(), true);

            // 验证必填字段
            $required = ['username', 'email', 'token'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return new JsonResponse(['error' => "字段 {$field} 不能为空"], 400);
                }
            }

            // 验证邮箱格式
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                return new JsonResponse(['error' => '邮箱格式不正确'], 400);
            }

            // 检查用户名是否已存在
            $githubAccountModel = new GitHubAccount();
            if ($githubAccountModel->findByUsername($data['username'])) {
                return new JsonResponse(['error' => 'GitHub用户名已存在'], 400);
            }

            // 创建GitHub账号
            $accountId = $githubAccountModel->create([
                'username' => $data['username'],
                'email' => $data['email'],
                'token' => $data['token'],
                'status' => 'active'
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'GitHub账号添加成功',
                'account_id' => $accountId
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '添加GitHub账号失败: ' . $e->getMessage()], 500);
        }
    }

    // 获取GitHub账号列表
    public function getGitHubAccounts(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $githubAccountModel = new GitHubAccount();
            $accounts = $githubAccountModel->getAllAccounts();

            // 为每个账号添加token状态信息
            foreach ($accounts as &$account) {
                $token = $githubAccountModel->getAccountToken($account['_id']);
                $account['has_token'] = !empty($token);
            }

            return new JsonResponse([
                'success' => true,
                'accounts' => $accounts
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '获取GitHub账号列表失败: ' . $e->getMessage()], 500);
        }
    }

    // 更新GitHub账号状态
    public function updateGitHubAccountStatus(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $data = json_decode($request->getContent(), true);

            if (empty($data['account_id']) || empty($data['status'])) {
                return new JsonResponse(['error' => '账号ID和状态不能为空'], 400);
            }

            $githubAccountModel = new GitHubAccount();
            $result = $githubAccountModel->updateStatus($data['account_id'], $data['status']);

            if ($result) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'GitHub账号状态更新成功'
                ]);
            } else {
                return new JsonResponse(['error' => 'GitHub账号不存在'], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '更新GitHub账号状态失败: ' . $e->getMessage()], 500);
        }
    }

    // 获取系统统计信息
    public function getSystemStats(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $userModel = new User();
            $activationCodeModel = new ActivationCode();
            $githubAccountModel = new GitHubAccount();
            $uploadRecordModel = new UploadRecord();

            $stats = [
                'total_users' => $userModel->countUsers(),
                'active_users' => $userModel->countUsers(['status' => 'active']),
                'total_activation_codes' => $activationCodeModel->countCodes(),
                'active_activation_codes' => $activationCodeModel->countCodes(['status' => 'active']),
                'total_github_accounts' => $githubAccountModel->countAccounts(),
                'active_github_accounts' => $githubAccountModel->countAccounts(['status' => 'active']),
                'total_uploads' => $uploadRecordModel->countRecords(),
                'queued_uploads' => $uploadRecordModel->countQueuedRecords(),
                'uploading_uploads' => $uploadRecordModel->countUploadingRecords()
            ];

            return new JsonResponse([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '获取系统统计信息失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 删除用户
     */
    public function deleteUser(Request $request): JsonResponse
    {
        $authHeader = $request->headers->get('Authorization');
        if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
            return new JsonResponse(['error' => '缺少认证token'], 401);
        }

        try {
            $token = substr($authHeader, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            // 获取用户ID
            $userId = $request->attributes->get('id');
            if (empty($userId)) {
                return new JsonResponse(['error' => '用户ID不能为空'], 400);
            }

            // 不能删除自己
            if ($userId === $user['_id']) {
                return new JsonResponse(['error' => '不能删除自己的账号'], 400);
            }

            $userModel = new User();

            // 检查用户是否存在
            $targetUser = $userModel->findById($userId);
            if (!$targetUser) {
                return new JsonResponse(['error' => '用户不存在'], 404);
            }

            // 不能删除其他管理员
            if ($targetUser['role'] === 'admin') {
                return new JsonResponse(['error' => '不能删除管理员账号'], 400);
            }

            // 删除用户
            $result = $userModel->deleteUser($userId);

            if ($result) {
                return new JsonResponse([
                    'success' => true,
                    'message' => '用户删除成功'
                ]);
            } else {
                return new JsonResponse(['error' => '删除用户失败'], 500);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '删除用户失败: ' . $e->getMessage()], 500);
        }
    }

    // 重置GitHub账号使用时长
    public function resetGitHubAccountUsage(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $accountId = $request->attributes->get('id');
            if (empty($accountId)) {
                return new JsonResponse(['error' => '账号ID不能为空'], 400);
            }

            $githubAccountModel = new GitHubAccount();
            $result = $githubAccountModel->resetUsageMinutes($accountId, 0);

            if ($result) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'GitHub账号使用时长重置成功'
                ]);
            } else {
                return new JsonResponse(['error' => 'GitHub账号不存在'], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '重置使用时长失败: ' . $e->getMessage()], 500);
        }
    }

    // 释放GitHub账号（标记为空闲）
    public function releaseGitHubAccount(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $accountId = $request->attributes->get('id');
            if (empty($accountId)) {
                return new JsonResponse(['error' => '账号ID不能为空'], 400);
            }

            $githubAccountModel = new GitHubAccount();
            $result = $githubAccountModel->markAsIdle($accountId);

            if ($result) {
                return new JsonResponse([
                    'success' => true,
                    'message' => 'GitHub账号已释放'
                ]);
            } else {
                return new JsonResponse(['error' => 'GitHub账号不存在'], 404);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '释放账号失败: ' . $e->getMessage()], 500);
        }
    }

    // 刷新所有GitHub账号状态
    public function refreshAllGitHubAccounts(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            // 这里可以添加刷新逻辑，比如检查GitHub API状态等
            // 目前只是返回成功，因为我们使用本地记录系统

            return new JsonResponse([
                'success' => true,
                'message' => '所有账号状态刷新成功'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '刷新账号状态失败: ' . $e->getMessage()], 500);
        }
    }

    // 重置所有GitHub账号使用时长
    public function resetAllGitHubAccountsUsage(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $token = substr($token, 7);
            $user = $this->authService->verifyToken($token);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '权限不足'], 403);
            }

            $githubAccountModel = new GitHubAccount();
            $accounts = $githubAccountModel->getAllAccounts();

            $resetCount = 0;
            foreach ($accounts as $account) {
                if ($githubAccountModel->resetUsageMinutes($account['_id'], 0)) {
                    $resetCount++;
                }
            }

            return new JsonResponse([
                'success' => true,
                'message' => "成功重置 {$resetCount} 个账号的使用时长"
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '重置所有使用时长失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取GitHub账号使用统计
     */
    public function getGitHubAccountUsageStats(Request $request, string $accountId): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $tokenValue = substr($token, 7);
            $user = $this->authService->validateToken($tokenValue);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '需要管理员权限'], 403);
            }

            // 获取GitHub账号使用统计
            $githubAccount = new GitHubAccount();
            $stats = $githubAccount->getUsageStats($accountId);

            if (!$stats) {
                return new JsonResponse(['error' => 'GitHub账号不存在'], 404);
            }

            return new JsonResponse($stats);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '获取使用统计失败: ' . $e->getMessage()], 500);
        }
    }

    /**
     * 获取所有GitHub账号的使用统计汇总
     */
    public function getAllGitHubUsageStats(Request $request): JsonResponse
    {
        try {
            // 验证管理员权限
            $token = $request->headers->get('Authorization');
            if (!$token || !str_starts_with($token, 'Bearer ')) {
                return new JsonResponse(['error' => '未授权访问'], 401);
            }

            $tokenValue = substr($token, 7);
            $user = $this->authService->validateToken($tokenValue);

            if (!$user || $user['role'] !== 'admin') {
                return new JsonResponse(['error' => '需要管理员权限'], 403);
            }

            // 获取所有GitHub账号
            $githubAccount = new GitHubAccount();
            $accounts = $githubAccount->getAllAccounts();

            // 为每个账号添加使用统计和token状态
            $accountsWithStats = [];
            foreach ($accounts as $account) {
                // 获取工作流执行统计
                $stats = $githubAccount->getUsageStats($account['_id']);

                // 获取token状态
                $token = $githubAccount->getAccountToken($account['_id']);
                $account['has_token'] = !empty($token);

                if ($stats) {
                    $accountsWithStats[] = array_merge($account, $stats);
                } else {
                    // 如果获取统计失败，使用默认值
                    $accountsWithStats[] = array_merge($account, [
                        'total_usage_seconds' => 0,
                        'usage_count' => 0,
                        'average_duration' => 0,
                        'total_usage_formatted' => '0秒',
                        'last_used_at' => null
                    ]);
                }
            }

            // 按总使用时长排序
            usort($accountsWithStats, function ($a, $b) {
                return ($b['total_usage_seconds'] ?? 0) - ($a['total_usage_seconds'] ?? 0);
            });

            return new JsonResponse([
                'accounts' => $accountsWithStats,
                'summary' => [
                    'total_accounts' => count($accountsWithStats),
                    'total_usage_seconds' => array_sum(array_column($accountsWithStats, 'total_usage_seconds')),
                    'total_usage_count' => array_sum(array_column($accountsWithStats, 'usage_count')),
                    'active_accounts' => count(array_filter($accountsWithStats, function ($acc) {
                        return $acc['status'] === 'active';
                    }))
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse(['error' => '获取使用统计失败: ' . $e->getMessage()], 500);
        }
    }
}

<?php

namespace App\Controllers;

use App\Models\User;
use App\Services\AuthService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;

class UserManagementController
{
    private User $userModel;
    private AuthService $authService;

    public function __construct()
    {
        $this->userModel = new User();
        $this->authService = new AuthService();
    }

    /**
     * 获取所有用户列表（管理员功能）
     */
    public function getUserList(Request $request): JsonResponse
    {
        // 验证管理员权限
        $admin = $this->getAuthenticatedAdmin($request);
        if (!$admin) {
            return new JsonResponse([
                'success' => false,
                'error' => '需要管理员权限'
            ], 403);
        }

        try {
            // 使用反射获取所有用户
            $reflection = new \ReflectionClass($this->userModel);
            $collectionProperty = $reflection->getProperty('collection');
            $collectionProperty->setAccessible(true);
            $collection = $collectionProperty->getValue($this->userModel);

            $cursor = $collection->find([]);
            $users = [];

            foreach ($cursor as $user) {
                $userData = [
                    'id' => (string)$user['_id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'status' => $user['status'],
                    'role' => $user['role'] ?? 'user',
                    'created_at' => isset($user['created_at']) ? $user['created_at']->toDateTime()->format('Y-m-d H:i:s') : null,
                    'last_login' => isset($user['last_login']) ? $user['last_login']->toDateTime()->format('Y-m-d H:i:s') : null
                ];

                // 处理过期时间
                if (isset($user['expires_at'])) {
                    if ($user['expires_at'] instanceof \MongoDB\BSON\UTCDateTime) {
                        $expiresTimestamp = $user['expires_at']->toDateTime()->getTimestamp();
                        $userData['expires_at'] = date('Y-m-d H:i:s', $expiresTimestamp);
                        $userData['is_expired'] = time() > $expiresTimestamp;
                        $userData['days_remaining'] = floor(($expiresTimestamp - time()) / (24 * 60 * 60));
                    }
                } else {
                    $userData['expires_at'] = null;
                    $userData['is_expired'] = false;
                    $userData['days_remaining'] = null;
                }

                $users[] = $userData;
            }

            return new JsonResponse([
                'success' => true,
                'users' => $users
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '获取用户列表失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 延长用户有效期
     */
    public function extendUserExpiry(Request $request): JsonResponse
    {
        // 验证管理员权限
        $admin = $this->getAuthenticatedAdmin($request);
        if (!$admin) {
            return new JsonResponse([
                'success' => false,
                'error' => '需要管理员权限'
            ], 403);
        }

        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['user_id']) || !isset($data['days'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少必要参数'
            ], 400);
        }

        $userId = $data['user_id'];
        $days = (int)$data['days'];

        if ($days <= 0 || $days > 365) {
            return new JsonResponse([
                'success' => false,
                'error' => '延长天数必须在1-365天之间'
            ], 400);
        }

        try {
            // 计算新的过期时间
            $newExpiryTimestamp = time() + ($days * 24 * 60 * 60);
            
            // 更新用户过期时间
            $result = $this->userModel->activateWithExpiry($userId, $newExpiryTimestamp);

            if ($result) {
                return new JsonResponse([
                    'success' => true,
                    'message' => "用户有效期已延长 {$days} 天",
                    'new_expiry' => date('Y-m-d H:i:s', $newExpiryTimestamp)
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'error' => '更新失败，用户可能不存在'
                ], 404);
            }

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '延长有效期失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 重置用户密码
     */
    public function resetUserPassword(Request $request): JsonResponse
    {
        // 验证管理员权限
        $admin = $this->getAuthenticatedAdmin($request);
        if (!$admin) {
            return new JsonResponse([
                'success' => false,
                'error' => '需要管理员权限'
            ], 403);
        }

        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['user_id']) || !isset($data['new_password'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少必要参数'
            ], 400);
        }

        $userId = $data['user_id'];
        $newPassword = $data['new_password'];

        if (strlen($newPassword) < 6) {
            return new JsonResponse([
                'success' => false,
                'error' => '密码长度至少6位'
            ], 400);
        }

        try {
            // 使用反射更新密码
            $reflection = new \ReflectionClass($this->userModel);
            $collectionProperty = $reflection->getProperty('collection');
            $collectionProperty->setAccessible(true);
            $collection = $collectionProperty->getValue($this->userModel);

            $result = $collection->updateOne(
                ['_id' => new \MongoDB\BSON\ObjectId($userId)],
                ['$set' => [
                    'password' => password_hash($newPassword, PASSWORD_DEFAULT),
                    'updated_at' => new \MongoDB\BSON\UTCDateTime()
                ]]
            );

            if ($result->getModifiedCount() > 0) {
                return new JsonResponse([
                    'success' => true,
                    'message' => '密码重置成功'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'error' => '用户不存在或密码未改变'
                ], 404);
            }

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '重置密码失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 禁用/启用用户
     */
    public function toggleUserStatus(Request $request): JsonResponse
    {
        // 验证管理员权限
        $admin = $this->getAuthenticatedAdmin($request);
        if (!$admin) {
            return new JsonResponse([
                'success' => false,
                'error' => '需要管理员权限'
            ], 403);
        }

        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['user_id']) || !isset($data['status'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '缺少必要参数'
            ], 400);
        }

        $userId = $data['user_id'];
        $status = $data['status'];

        if (!in_array($status, ['active', 'inactive'])) {
            return new JsonResponse([
                'success' => false,
                'error' => '状态值无效'
            ], 400);
        }

        try {
            // 使用反射更新状态
            $reflection = new \ReflectionClass($this->userModel);
            $collectionProperty = $reflection->getProperty('collection');
            $collectionProperty->setAccessible(true);
            $collection = $collectionProperty->getValue($this->userModel);

            $result = $collection->updateOne(
                ['_id' => new \MongoDB\BSON\ObjectId($userId)],
                ['$set' => [
                    'status' => $status,
                    'updated_at' => new \MongoDB\BSON\UTCDateTime()
                ]]
            );

            if ($result->getModifiedCount() > 0) {
                return new JsonResponse([
                    'success' => true,
                    'message' => '用户状态更新成功'
                ]);
            } else {
                return new JsonResponse([
                    'success' => false,
                    'error' => '用户不存在或状态未改变'
                ], 404);
            }

        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => '更新状态失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取认证的管理员用户
     */
    private function getAuthenticatedAdmin(Request $request): ?array
    {
        $token = $request->headers->get('Authorization');
        if (!$token || !str_starts_with($token, 'Bearer ')) {
            return null;
        }

        $token = substr($token, 7);
        $user = $this->authService->verifyToken($token);

        if (!$user || ($user['role'] ?? 'user') !== 'admin') {
            return null;
        }

        return $user;
    }
}

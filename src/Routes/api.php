<?php

use App\Controllers\AuthController;
use App\Controllers\UploadController;
use App\Controllers\WorkflowController;
use App\Controllers\AdminController;
use App\Controllers\CertificateController;
use App\Controllers\CerConverterController;
use App\Controllers\ResignController;
use App\Controllers\DistributionController;
use App\Controllers\UserManagementController;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

$routes = new RouteCollection();

// 认证相关路由
$routes->add('auth_login', new Route('/api/auth/login', [
    '_controller' => [AuthController::class, 'login']
], [], [], '', [], ['POST']));

$routes->add('auth_activate', new Route('/api/auth/activate', [
    '_controller' => [AuthController::class, 'activate']
], [], [], '', [], ['POST']));

$routes->add('auth_verify', new Route('/api/auth/verify', [
    '_controller' => [AuthController::class, 'verify']
], [], [], '', [], ['GET']));

$routes->add('auth_refresh', new Route('/api/auth/refresh', [
    '_controller' => [AuthController::class, 'refresh']
], [], [], '', [], ['POST']));

$routes->add('auth_validate_api_key', new Route('/api/auth/validate-api-key', [
    '_controller' => [AuthController::class, 'validateAPIKey']
], [], [], '', [], ['POST']));

$routes->add('auth_validate_apple_id', new Route('/api/auth/validate-apple-id', [
    '_controller' => [AuthController::class, 'validateAppleID']
], [], [], '', [], ['POST']));

// 上传相关路由
$routes->add('upload_ipa', new Route('/api/upload/ipa', [
    '_controller' => [UploadController::class, 'uploadIPA']
], [], [], '', [], ['POST']));

$routes->add('upload_records', new Route('/api/upload/records', [
    '_controller' => [UploadController::class, 'getRecords']
], [], [], '', [], ['GET']));

$routes->add('parse_ipa', new Route('/api/upload/parse-ipa', [
    '_controller' => [UploadController::class, 'parseIPA']
], [], [], '', [], ['POST']));

$routes->add('upload_file', new Route('/api/upload/file', [
    '_controller' => [UploadController::class, 'uploadFile']
], [], [], '', [], ['POST']));

$routes->add('upload_record_detail', new Route('/api/upload/records/{recordId}', [
    '_controller' => [UploadController::class, 'getRecordDetail']
], [], [], '', [], ['GET']));

$routes->add('upload_queue_status', new Route('/api/upload/queue', [
    '_controller' => [UploadController::class, 'getQueueStatus']
], [], [], '', [], ['GET']));

$routes->add('upload_webhook', new Route('/api/upload/webhook', [
    '_controller' => [UploadController::class, 'webhook']
], [], [], '', [], ['POST']));

$routes->add('upload_progress', new Route('/api/upload/progress/{uploadId}', [
    '_controller' => [UploadController::class, 'getProgress']
], [], [], '', [], ['GET']));

$routes->add('download_ipa', new Route('/api/upload/download/{recordId}', [
    '_controller' => [UploadController::class, 'downloadIPA']
], [], [], '', [], ['GET']));

$routes->add('generate_temp_download_link', new Route('/api/upload/temp-link/{recordId}', [
    '_controller' => [UploadController::class, 'generateTempDownloadLink']
], [], [], '', [], ['POST']));

$routes->add('temp_download_ipa', new Route('/api/upload/temp-download/{recordId}', [
    '_controller' => [UploadController::class, 'tempDownload']
], [], [], '', [], ['GET']));

$routes->add('workflow_status_callback', new Route('/api/upload/workflow-callback', [
    '_controller' => [UploadController::class, 'workflowCallback']
], [], [], '', [], ['POST']));

// 工作流相关路由
$routes->add('workflow_callback', new Route('/api/workflow/callback', [
    '_controller' => [WorkflowController::class, 'callback']
], [], [], '', [], ['POST']));

$routes->add('workflow_download_ipa', new Route('/download/ipa/{recordId}', [
    '_controller' => [WorkflowController::class, 'downloadIPA']
], [], [], '', [], ['GET']));

// 管理员路由
$routes->add('admin_create_user', new Route('/api/admin/users', [
    '_controller' => [AdminController::class, 'createUser']
], [], [], '', [], ['POST']));

$routes->add('admin_get_users', new Route('/api/admin/users', [
    '_controller' => [AdminController::class, 'getUsers']
], [], [], '', [], ['GET']));

$routes->add('admin_delete_user', new Route('/api/admin/users/{id}', [
    '_controller' => [AdminController::class, 'deleteUser']
], [], [], '', [], ['DELETE']));

$routes->add('admin_create_activation_code', new Route('/api/admin/activation-codes', [
    '_controller' => [AdminController::class, 'createActivationCode']
], [], [], '', [], ['POST']));

$routes->add('admin_get_activation_codes', new Route('/api/admin/activation-codes', [
    '_controller' => [AdminController::class, 'getActivationCodes']
], [], [], '', [], ['GET']));

$routes->add('admin_add_github_account', new Route('/api/admin/github-accounts', [
    '_controller' => [AdminController::class, 'addGitHubAccount']
], [], [], '', [], ['POST']));

$routes->add('admin_get_github_accounts', new Route('/api/admin/github-accounts', [
    '_controller' => [AdminController::class, 'getGitHubAccounts']
], [], [], '', [], ['GET']));

$routes->add('admin_update_github_account_status', new Route('/api/admin/github-accounts/{id}/status', [
    '_controller' => [AdminController::class, 'updateGitHubAccountStatus']
], [], [], '', [], ['PUT']));

$routes->add('admin_reset_github_account_usage', new Route('/api/admin/github-accounts/{id}/reset-usage', [
    '_controller' => [AdminController::class, 'resetGitHubAccountUsage']
], [], [], '', [], ['POST']));

$routes->add('admin_release_github_account', new Route('/api/admin/github-accounts/{id}/release', [
    '_controller' => [AdminController::class, 'releaseGitHubAccount']
], [], [], '', [], ['POST']));

$routes->add('admin_refresh_all_github_accounts', new Route('/api/admin/github-accounts/refresh-all', [
    '_controller' => [AdminController::class, 'refreshAllGitHubAccounts']
], [], [], '', [], ['POST']));

$routes->add('admin_reset_all_github_accounts_usage', new Route('/api/admin/github-accounts/reset-all-usage', [
    '_controller' => [AdminController::class, 'resetAllGitHubAccountsUsage']
], [], [], '', [], ['POST']));

$routes->add('admin_get_system_stats', new Route('/api/admin/stats', [
    '_controller' => [AdminController::class, 'getSystemStats']
], [], [], '', [], ['GET']));

// GitHub账号使用统计路由
$routes->add('admin_get_github_account_usage_stats', new Route('/api/admin/github-accounts/{accountId}/usage-stats', [
    '_controller' => [AdminController::class, 'getGitHubAccountUsageStats']
], [], [], '', [], ['GET']));

$routes->add('admin_get_all_github_usage_stats', new Route('/api/admin/github-usage-stats', [
    '_controller' => [AdminController::class, 'getAllGitHubUsageStats']
], [], [], '', [], ['GET']));

// 证书管理路由
$routes->add('certificate_generate_csr', new Route('/api/certificates/csr/generate', [
    '_controller' => [CertificateController::class, 'generateCsr']
], [], [], '', [], ['POST']));

$routes->add('certificate_download_csr', new Route('/api/certificates/csr/{certificateId}/download', [
    '_controller' => [CertificateController::class, 'downloadCsr']
], [], [], '', [], ['GET']));

$routes->add('certificate_get_user_csr_list', new Route('/api/certificates/csr', [
    '_controller' => [CertificateController::class, 'getUserCsrList']
], [], [], '', [], ['GET']));

$routes->add('certificate_upload_p12', new Route('/api/certificates/p12', [
    '_controller' => [CertificateController::class, 'uploadP12Certificate']
], [], [], '', [], ['POST']));

$routes->add('certificate_upload_mobileprovision', new Route('/api/certificates/mobileprovision', [
    '_controller' => [CertificateController::class, 'uploadMobileProvision']
], [], [], '', [], ['POST']));

$routes->add('certificate_get_all', new Route('/api/certificates', [
    '_controller' => [CertificateController::class, 'getAllCertificates']
], [], [], '', [], ['GET']));

$routes->add('certificate_get_pairs', new Route('/api/certificates/pairs', [
    '_controller' => [CertificateController::class, 'getAvailableCertificatePairs']
], [], [], '', [], ['GET']));

$routes->add('certificate_delete', new Route('/api/certificates/{certificateId}', [
    '_controller' => [CertificateController::class, 'deleteCertificate']
], [], [], '', [], ['DELETE']));

$routes->add('certificate_get_detail', new Route('/api/certificates/{certificateId}', [
    '_controller' => [CertificateController::class, 'getCertificateDetail']
], [], [], '', [], ['GET']));

// CER转P12路由（智能版）
$routes->add('cer_convert_to_p12', new Route('/api/certificates/cer/convert', [
    '_controller' => [CerConverterController::class, 'convertCerToP12']
], [], [], '', [], ['POST']));

$routes->add('cer_download_p12', new Route('/api/certificates/cer/{certificateId}/download', [
    '_controller' => [CerConverterController::class, 'downloadP12']
], [], [], '', [], ['GET']));

$routes->add('cer_conversion_guide', new Route('/api/certificates/cer/guide', [
    '_controller' => [CerConverterController::class, 'getConversionGuide']
], [], [], '', [], ['GET']));

$routes->add('cer_conversion_records', new Route('/api/certificates/cer/records', [
    '_controller' => [CerConverterController::class, 'getConversionRecords']
], [], [], '', [], ['GET']));

// 用户管理路由（管理员功能）
$routes->add('admin_user_list', new Route('/api/admin/users', [
    '_controller' => [UserManagementController::class, 'getUserList']
], [], [], '', [], ['GET']));

$routes->add('admin_extend_user_expiry', new Route('/api/admin/users/extend-expiry', [
    '_controller' => [UserManagementController::class, 'extendUserExpiry']
], [], [], '', [], ['POST']));

$routes->add('admin_reset_user_password', new Route('/api/admin/users/reset-password', [
    '_controller' => [UserManagementController::class, 'resetUserPassword']
], [], [], '', [], ['POST']));

$routes->add('admin_toggle_user_status', new Route('/api/admin/users/toggle-status', [
    '_controller' => [UserManagementController::class, 'toggleUserStatus']
], [], [], '', [], ['POST']));

// 重签相关路由
$routes->add('resign_submit_task', new Route('/api/resign/submit', [
    '_controller' => [ResignController::class, 'submitResignTask']
], [], [], '', [], ['POST']));

$routes->add('resign_get_user_records', new Route('/api/resign/records', [
    '_controller' => [ResignController::class, 'getUserResignRecords']
], [], [], '', [], ['GET']));

$routes->add('resign_delete_user_record', new Route('/api/resign/records/{recordId}', [
    '_controller' => [ResignController::class, 'deleteResignRecord']
], [], [], '', [], ['DELETE']));

$routes->add('resign_get_record_detail', new Route('/api/resign/records/{recordId}', [
    '_controller' => [ResignController::class, 'getResignRecordDetail']
], [], [], '', [], ['GET']));

$routes->add('resign_download_ipa', new Route('/api/resign/download/{recordId}', [
    '_controller' => [ResignController::class, 'downloadResignedIpa']
], [], [], '', [], ['GET']));

$routes->add('resign_retry_task', new Route('/api/resign/retry/{recordId}', [
    '_controller' => [ResignController::class, 'retryResignTask']
], [], [], '', [], ['POST']));

$routes->add('resign_get_all_records', new Route('/api/admin/resign/records', [
    '_controller' => [ResignController::class, 'getAllResignRecords']
], [], [], '', [], ['GET']));

$routes->add('resign_delete_record', new Route('/api/admin/resign/records/{recordId}', [
    '_controller' => [ResignController::class, 'deleteResignRecord']
], [], [], '', [], ['DELETE']));

// 分发相关路由
$routes->add('distribution_create', new Route('/api/distribution/create', [
    '_controller' => [DistributionController::class, 'createDistribution']
], [], [], '', [], ['POST']));

$routes->add('distribution_install_page', new Route('/api/distribution/install/{distributionId}', [
    '_controller' => [DistributionController::class, 'installPage']
], [], [], '', [], ['GET']));

$routes->add('distribution_get_plist', new Route('/api/distribution/plist/{distributionId}', [
    '_controller' => [DistributionController::class, 'getPlist']
], [], [], '', [], ['GET']));

$routes->add('distribution_download_ipa', new Route('/api/distribution/download/{distributionId}', [
    '_controller' => [DistributionController::class, 'downloadIpa']
], [], [], '', [], ['GET']));



$routes->add('distribution_get_user_records', new Route('/api/distribution/records', [
    '_controller' => [DistributionController::class, 'getUserDistributions']
], [], [], '', [], ['GET']));

$routes->add('distribution_get_stats', new Route('/api/admin/distribution/stats', [
    '_controller' => [DistributionController::class, 'getDistributionStats']
], [], [], '', [], ['GET']));

return $routes;

#!/bin/bash

# XIOS iOS应用管理工具 - 一键部署脚本
# 适用于Ubuntu 22.04 LTS

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        echo "请使用: sudo $0"
        exit 1
    fi
}

# 系统信息检查
check_system() {
    log_info "检查系统信息..."

    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测操作系统版本"
        exit 1
    fi

    source /etc/os-release
    if [[ "$ID" != "ubuntu" ]]; then
        log_warning "此脚本专为Ubuntu设计，当前系统: $ID"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi

    log_success "系统检查完成: $PRETTY_NAME"
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    apt update
    apt upgrade -y
    log_success "系统更新完成"
}

# 安装基础依赖
install_basic_deps() {
    log_info "安装基础依赖..."
    apt install -y \
        curl \
        wget \
        unzip \
        git \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        openssl
    log_success "基础依赖安装完成"
}

# 安装PHP 8.3
install_php() {
    log_info "安装PHP 8.3..."

    # 添加PHP仓库
    add-apt-repository ppa:ondrej/php -y
    apt update

    # 安装PHP及扩展
    apt install -y \
        php8.3 \
        php8.3-fpm \
        php8.3-cli \
        php8.3-common \
        php8.3-mysql \
        php8.3-zip \
        php8.3-gd \
        php8.3-mbstring \
        php8.3-curl \
        php8.3-xml \
        php8.3-bcmath \
        php8.3-json \
        php8.3-intl \
        php8.3-mongodb

    # 启动PHP-FPM
    systemctl enable php8.3-fpm
    systemctl start php8.3-fpm

    log_success "PHP 8.3安装完成"
}

# 安装Composer
install_composer() {
    log_info "安装Composer..."

    # 下载Composer安装器
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer

    # 验证安装
    composer --version

    log_success "Composer安装完成"
}

# 安装MongoDB
install_mongodb() {
    log_info "安装MongoDB..."

    # 导入MongoDB公钥
    curl -fsSL https://pgp.mongodb.com/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor

    # 添加MongoDB仓库
    echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list

    apt update
    apt install -y mongodb-org

    # 启动MongoDB
    systemctl enable mongod
    systemctl start mongod

    log_success "MongoDB安装完成"
}

# 安装zsign
install_zsign() {
    log_info "安装zsign..."

    # 检查是否已安装
    if command -v zsign &> /dev/null; then
        log_warning "zsign已安装，跳过"
        return
    fi

    # 安装编译依赖
    apt-get install -y git g++ pkg-config libssl-dev libminizip-dev

    # 克隆zsign仓库到临时目录
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    git clone https://github.com/zhlynn/zsign.git
    cd zsign/build/linux

    # 编译zsign
    make clean && make

    # 安装到系统路径
    cp zsign /usr/local/bin/
    chmod +x /usr/local/bin/zsign

    # 清理临时目录
    cd /
    rm -rf "$TEMP_DIR"

    # 验证安装
    if command -v zsign &> /dev/null; then
        log_success "zsign安装完成"
    else
        log_error "zsign安装失败"
        exit 1
    fi
}

# 安装Caddy
install_caddy() {
    log_info "安装Caddy..."

    # 添加Caddy仓库
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list

    apt update
    apt install -y caddy

    # 启动Caddy
    systemctl enable caddy
    systemctl start caddy

    log_success "Caddy安装完成"
}

# 创建项目目录
create_project_dirs() {
    log_info "创建项目目录..."

    # 创建主目录
    mkdir -p /data/www/api.ios.xxyx.cn
    mkdir -p /data/www/ios.xxyx.cn
    mkdir -p /data/logs/access
    mkdir -p /data/logs/error

    # 设置权限
    chown -R caddy:caddy /data/www
    chown -R caddy:caddy /data/logs

    log_success "项目目录创建完成"
}

# 配置Caddy
configure_caddy() {
    log_info "配置Caddy..."

    # 创建Caddy配置目录
    mkdir -p /etc/caddy/sites

    # API服务器配置
    cat > /etc/caddy/sites/api.ios.xxyx.cn.conf << 'EOF'
api.ios.xxyx.cn {
    root * /data/www/api.ios.xxyx.cn/public

    # PHP处理
    php_fastcgi unix//run/php/php8.3-fpm.sock

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 日志记录
    log {
        output file /data/logs/access/api.ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

    # 前端配置
    cat > /etc/caddy/sites/ios.xxyx.cn.conf << 'EOF'
ios.xxyx.cn {
    root * /data/www/ios.xxyx.cn

    # 智能路由处理
    try_files {path} {path}/index.html {path}/index.php /index.html

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # PHP处理
    @php path *.php
    php_fastcgi @php unix//run/php/php8.3-fpm.sock

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 静态资源缓存
    @static {
        file
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"

    # 日志记录
    log {
        output file /data/logs/access/ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

    # 主配置文件
    cat > /etc/caddy/Caddyfile << 'EOF'
{
    # 全局配置
    admin localhost:2019

    # 日志配置
    log {
        output file /data/logs/caddy.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
        level INFO
    }
}

# 导入站点配置
import /etc/caddy/sites/*.conf
EOF

    # 重新加载Caddy配置
    systemctl reload caddy

    log_success "Caddy配置完成"
}

# 部署项目代码
deploy_project() {
    log_info "部署项目代码..."

    # 检查当前目录是否为项目根目录
    if [[ ! -f "composer.json" ]]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi

    # 复制API代码
    log_info "复制API代码到 /data/www/api.ios.xxyx.cn..."
    rsync -av --exclude='.git' --exclude='node_modules' --exclude='storage/logs' ./ /data/www/api.ios.xxyx.cn/

    # 复制前端代码
    log_info "复制前端代码到 /data/www/ios.xxyx.cn..."
    rsync -av frontend/ /data/www/ios.xxyx.cn/

    # 设置权限
    chown -R caddy:caddy /data/www/api.ios.xxyx.cn
    chown -R caddy:caddy /data/www/ios.xxyx.cn

    log_success "项目代码部署完成"
}

# 安装项目依赖
install_project_deps() {
    log_info "安装项目依赖..."

    cd /data/www/api.ios.xxyx.cn

    # 安装Composer依赖
    composer install --no-dev --optimize-autoloader

    # 创建必要目录
    mkdir -p storage/logs
    mkdir -p storage/uploads/ipa
    mkdir -p storage/uploads/icons
    mkdir -p storage/uploads/upload_tmp

    # 设置权限
    chmod -R 755 storage
    chmod +x bin/queue-processor.php
    chown -R caddy:caddy storage

    log_success "项目依赖安装完成"
}

# 生成密钥
generate_keys() {
    log_info "生成项目密钥..."

    cd /data/www/api.ios.xxyx.cn

    # 生成随机密钥
    local jwt_secret=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-64)
    local encryption_key=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)
    local app_key=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-32)

    # 直接创建包含密钥的.env文件
    cat > .env << EOF
# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_KEY=$app_key

# JWT配置
JWT_SECRET=$jwt_secret
JWT_EXPIRE=86400

# 加密配置
ENCRYPTION_KEY=$encryption_key

# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/xios

# GitHub配置
GITHUB_WEBHOOK_SECRET=your_webhook_secret_here

# 日志配置
LOG_LEVEL=info
LOG_PATH=storage/logs

# 上传配置
UPLOAD_MAX_SIZE=100M
UPLOAD_PATH=storage/uploads
EOF

    log_success "密钥生成完成"
}

# 创建管理员账户
create_admin_user() {
    log_info "创建管理员账户..."

    cd /data/www/api.ios.xxyx.cn

    # 创建管理员用户脚本
    cat > create_admin.php << 'EOF'
<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Services\AuthService;

try {
    echo "创建管理员账户...\n";

    $user = new User();
    $authService = new AuthService();

    // 检查是否已存在管理员
    $existingAdmin = $user->findByEmail('<EMAIL>');
    if ($existingAdmin) {
        echo "管理员账户已存在，跳过创建\n";
        exit(0);
    }

    // 创建管理员账户
    $adminData = [
        'username' => 'admin',
        'email' => '<EMAIL>',
        'password' => password_hash('admin123456', PASSWORD_DEFAULT),
        'role' => 'admin',
        'status' => 'active',
        'created_at' => new \MongoDB\BSON\UTCDateTime(),
        'updated_at' => new \MongoDB\BSON\UTCDateTime()
    ];

    $result = $user->create($adminData);

    if ($result) {
        echo "✅ 管理员账户创建成功！\n";
        echo "用户名: admin\n";
        echo "邮箱: <EMAIL>\n";
        echo "密码: admin123456\n";
        echo "⚠️  请登录后立即修改密码！\n";
    } else {
        echo "❌ 管理员账户创建失败\n";
        exit(1);
    }

} catch (Exception $e) {
    echo "❌ 创建管理员账户时出错: " . $e->getMessage() . "\n";
    exit(1);
}
EOF

    # 执行创建管理员脚本
    php create_admin.php

    # 删除临时脚本
    rm -f create_admin.php

    log_success "管理员账户配置完成"
}

# 启动服务
start_services() {
    log_info "启动系统服务..."

    # 启动队列处理器
    cd /data/www/api.ios.xxyx.cn

    # 创建systemd服务文件
    cat > /etc/systemd/system/xios-queue.service << 'EOF'
[Unit]
Description=XIOS Queue Processor
After=network.target mongod.service

[Service]
Type=simple
User=caddy
Group=caddy
WorkingDirectory=/data/www/api.ios.xxyx.cn
ExecStart=/usr/bin/php bin/queue-processor.php
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用并启动服务
    systemctl daemon-reload
    systemctl enable xios-queue
    systemctl start xios-queue

    log_success "队列处理器服务启动完成"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."

    # 测试MongoDB连接
    cd /data/www/api.ios.xxyx.cn
    php -r "
    require_once 'vendor/autoload.php';
    try {
        \$config = App\Config\AppConfig::getInstance();
        \$db = App\Database\MongoDB::getInstance();
        echo 'MongoDB连接成功\n';
    } catch (Exception \$e) {
        echo 'MongoDB连接失败: ' . \$e->getMessage() . '\n';
        exit(1);
    }
    " || {
        log_error "MongoDB连接测试失败"
        exit 1
    }

    # 测试Web服务
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|301\|302"; then
        log_success "Web服务测试通过"
    else
        log_warning "Web服务可能未正常启动"
    fi

    # 检查服务状态
    systemctl is-active --quiet php8.3-fpm && log_success "PHP-FPM运行正常" || log_error "PHP-FPM未运行"
    systemctl is-active --quiet caddy && log_success "Caddy运行正常" || log_error "Caddy未运行"
    systemctl is-active --quiet mongod && log_success "MongoDB运行正常" || log_error "MongoDB未运行"
    systemctl is-active --quiet xios-queue && log_success "队列处理器运行正常" || log_error "队列处理器未运行"

    log_success "部署测试完成"
}

# 显示部署结果
show_deployment_info() {
    echo ""
    echo "🎉 XIOS部署完成！"
    echo ""
    echo "📋 服务信息："
    echo "- API服务器: https://api.ios.xxyx.cn"
    echo "- 前端网站: https://ios.xxyx.cn"
    echo "- 队列处理器: systemctl status xios-queue"
    echo ""
    echo "� 管理员账户："
    echo "- 用户名: admin"
    echo "- 邮箱: <EMAIL>"
    echo "- 密码: admin123456"
    echo "- ⚠️  请登录后立即修改密码！"
    echo ""
    echo "�📁 目录结构："
    echo "- API代码: /data/www/api.ios.xxyx.cn"
    echo "- 前端代码: /data/www/ios.xxyx.cn"
    echo "- 日志目录: /data/logs"
    echo ""
    echo "🔧 管理命令："
    echo "- 重启PHP-FPM: systemctl restart php8.3-fpm"
    echo "- 重启Caddy: systemctl restart caddy"

    # 修复存储目录权限
    echo "🔧 修复存储目录权限..."
    mkdir -p /data/storage/uploads/icons
    mkdir -p /data/storage/uploads/ipa
    mkdir -p /data/storage/resigned_ipas
    mkdir -p /data/storage/plists
    mkdir -p /data/storage/qrcodes
    mkdir -p /data/storage/temp

    # 设置正确的权限和所有者
    chown -R caddy:caddy /data/storage
    chmod -R 755 /data/storage

    # 确保PHP进程可以写入
    chmod -R 775 /data/storage/uploads
    chmod -R 775 /data/storage/temp

    echo "✅ 存储目录权限修复完成"
    echo "- 重启队列: systemctl restart xios-queue"
    echo "- 查看队列日志: journalctl -u xios-queue -f"
    echo ""
    echo "⚠️  重要提示："
    echo "1. 请配置域名DNS解析到此服务器"
    echo "2. 编辑 /data/www/api.ios.xxyx.cn/.env 配置MongoDB认证信息"
    echo "3. 访问前端页面进行功能测试"
    echo "4. 登录管理后台创建普通用户和激活码"
    echo ""
}

# 主部署流程
main() {
    echo "=========================================="
    echo "🎯 XIOS iOS应用管理工具 - 一键部署"
    echo "=========================================="
    echo ""

    # 检查运行环境
    check_root
    check_system

    echo ""
    log_info "开始系统部署..."
    echo ""

    # 系统准备
    update_system
    install_basic_deps

    # 安装服务
    install_php
    install_composer
    install_mongodb
    install_zsign
    install_caddy

    # 项目配置
    create_project_dirs
    configure_caddy

    # 部署项目
    deploy_project
    install_project_deps
    generate_keys

    # 创建管理员账户
    create_admin_user

    # 启动服务
    start_services

    # 测试部署
    test_deployment

    # 显示结果
    show_deployment_info

    log_success "XIOS部署完成！"
}

# 检查参数
if [[ $# -gt 0 ]]; then
    case $1 in
        --help|-h)
            echo "XIOS 一键部署脚本"
            echo ""
            echo "用法: sudo $0"
            echo ""
            echo "此脚本将自动安装和配置："
            echo "- PHP 8.3 + 扩展"
            echo "- Composer"
            echo "- MongoDB 7.0"
            echo "- zsign (iOS重签名工具)"
            echo "- Caddy Web服务器"
            echo "- XIOS项目代码"
            echo ""
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
fi

# 执行主流程
main

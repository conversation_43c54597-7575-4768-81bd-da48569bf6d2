# 图标显示问题修复指南

## 问题描述

图标路径正确 (`https://ios.xxyx.cn/storage/uploads/icons/resign_xxx.png`)，但是图片不显示。

## 问题原因

Web服务器无法访问存储目录 `/data/storage/uploads/icons/`，因为该目录不在Web服务器的文档根目录下。

## 解决方案

### 🚀 自动修复（推荐）

运行自动修复脚本：

```bash
# 给脚本添加执行权限
chmod +x scripts/fix-icon-display.sh

# 运行修复脚本（需要root权限）
sudo ./scripts/fix-icon-display.sh
```

### 🔧 手动修复方案

#### 方案1：更新Caddy配置

```bash
# 给脚本添加执行权限
chmod +x scripts/update-caddy-config.sh

# 运行配置更新脚本
sudo ./scripts/update-caddy-config.sh
```

#### 方案2：创建符号链接

```bash
# 给脚本添加执行权限
chmod +x scripts/setup-storage-symlinks.sh

# 运行符号链接脚本
sudo ./scripts/setup-storage-symlinks.sh
```

### 🧪 测试验证

运行测试脚本验证修复效果：

```bash
# PHP测试脚本
php scripts/test-icon-access.php

# 或者直接用curl测试
curl -I https://ios.xxyx.cn/storage/uploads/icons/test.png
```

## 修复原理

### Caddy配置方案

在Caddy配置中添加 `handle_path /storage/*` 指令，将 `/storage/*` 路径映射到 `/data/storage/` 目录：

```caddy
ios.xxyx.cn {
    root * /data/www/ios.xxyx.cn

    # 存储文件访问 - 优先处理
    handle_path /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
        header Cache-Control "public, max-age=86400"
    }
    
    # 其他配置...
}
```

### 符号链接方案

创建符号链接将存储目录链接到Web可访问的位置：

```bash
# 在前端网站根目录创建符号链接
ln -sf /data/storage /data/www/ios.xxyx.cn/storage

# 在API网站public目录创建符号链接
ln -sf /data/storage /data/www/api.ios.xxyx.cn/public/storage
```

## 验证步骤

1. **检查目录权限**：
   ```bash
   ls -la /data/storage/uploads/icons/
   ```

2. **测试HTTP访问**：
   ```bash
   curl -I https://ios.xxyx.cn/storage/uploads/icons/test.png
   ```

3. **检查Web服务器状态**：
   ```bash
   systemctl status caddy
   ```

4. **查看Web服务器日志**：
   ```bash
   tail -f /data/logs/access/ios.xxyx.cn.log
   ```

## 常见问题

### 1. HTTP 404 错误

**原因**：Web服务器配置未正确映射存储路径

**解决**：
- 检查Caddy配置中的 `handle_path /storage/*` 是否正确
- 确认符号链接是否创建成功

### 2. HTTP 403 错误

**原因**：文件权限问题

**解决**：
```bash
# 设置正确的权限
sudo chown -R caddy:caddy /data/storage
sudo chmod -R 755 /data/storage
sudo chmod 644 /data/storage/uploads/icons/*.png
```

### 3. HTTP 500 错误

**原因**：Web服务器内部错误

**解决**：
- 检查Web服务器错误日志
- 验证Caddy配置语法是否正确

### 4. SSL证书问题

**原因**：HTTPS访问时SSL证书验证失败

**解决**：
```bash
# 测试时跳过SSL验证
curl -k -I https://ios.xxyx.cn/storage/uploads/icons/test.png
```

## 目录结构

修复后的目录结构：

```
/data/
├── storage/
│   └── uploads/
│       ├── icons/          # 图标文件存储
│       ├── ipa/           # IPA文件存储
│       └── resigned_ipas/ # 重签后的IPA文件
├── www/
│   ├── ios.xxyx.cn/       # 前端网站
│   │   └── storage -> /data/storage  # 符号链接（可选）
│   └── api.ios.xxyx.cn/   # API网站
│       └── public/
│           └── storage -> /data/storage  # 符号链接（可选）
└── logs/                  # 日志目录
```

## 访问URL格式

修复后，图标的访问URL格式为：

```
https://ios.xxyx.cn/storage/uploads/icons/resign_[record_id].png
```

例如：
```
https://ios.xxyx.cn/storage/uploads/icons/resign_68a81211444e6bcee8027c02.png
```

## 注意事项

1. **权限设置**：确保Web服务器用户（通常是`caddy`）有权限访问存储目录
2. **缓存设置**：图标文件设置了适当的缓存头，提高访问性能
3. **安全考虑**：隐藏了敏感文件（.env, .git等）
4. **备份配置**：修改配置前会自动备份原配置文件

## 故障排除

如果修复后仍然无法显示图标，请按以下步骤排查：

1. **检查脚本执行结果**：
   ```bash
   ./scripts/fix-icon-display.sh
   ```

2. **手动测试访问**：
   ```bash
   curl -v https://ios.xxyx.cn/storage/uploads/icons/test.png
   ```

3. **检查Web服务器配置**：
   ```bash
   caddy validate --config /etc/caddy/Caddyfile
   ```

4. **查看实时日志**：
   ```bash
   tail -f /data/logs/access/ios.xxyx.cn.log
   ```

5. **联系技术支持**：如果问题仍然存在，请提供以上命令的输出结果。

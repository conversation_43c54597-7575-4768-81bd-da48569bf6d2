<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="XIOS - 登录页面">
    <title>登录 - XIOS</title>

    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/form.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <link rel="stylesheet" href="assets/css/theme.css">

    <style>
        body {
            background: var(--gradient-hero);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-4);
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translate(0, 0) rotate(0deg);
            }

            33% {
                transform: translate(30px, -30px) rotate(120deg);
            }

            66% {
                transform: translate(-20px, 20px) rotate(240deg);
            }
        }

        .login-container {
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .login-logo {
            font-size: var(--text-5xl);
            margin-bottom: var(--space-4);
        }

        .login-title {
            color: var(--text-inverse);
            font-size: var(--text-3xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-2);
        }

        .login-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--text-base);
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.1);
            padding: var(--space-8);
            position: relative;
            z-index: 1;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-6);
        }

        .login-footer {
            text-align: center;
            margin-top: var(--space-8);
        }

        .login-footer-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: var(--text-sm);
        }

        .error-message {
            background: var(--error-light);
            color: var(--error-600);
            padding: var(--space-3);
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
            margin-bottom: var(--space-4);
            display: none;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            background: var(--bg-primary);
            padding: var(--space-6);
            border-radius: var(--radius-xl);
            text-align: center;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .spinner {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-primary);
            border-top-color: var(--primary-500);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: var(--space-2);
        }
    </style>
</head>

<body>
    <div class="login-container">
        <!-- 登录头部 -->
        <div class="login-header">
            <div class="login-logo">📱</div>
            <h1 class="login-title">XIOS</h1>
            <p class="login-subtitle">iOS应用管理工具</p>
        </div>

        <!-- 登录卡片 -->
        <div class="login-card">
            <!-- 错误消息 -->
            <div class="error-message" id="error-message"></div>

            <!-- 登录表单 -->
            <form class="login-form" id="login-form">
                <div class="form-group">
                    <label class="form-label" for="username">用户名</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" required
                        autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">密码</label>
                    <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码"
                        required autocomplete="current-password">
                </div>

                <div class="form-group">
                    <div class="form-checkbox-item">
                        <input type="checkbox" id="remember" name="remember" class="form-checkbox">
                        <label for="remember">记住我</label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary btn-lg btn-block" id="login-btn">
                    <span>🚀</span>
                    <span>登录</span>
                </button>
            </form>
        </div>

        <!-- 登录底部 -->
        <div class="login-footer">
            <p class="login-footer-text">
                © 2024 XIOS. 专业的iOS应用管理工具
            </p>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <span>登录中...</span>
        </div>
    </div>

    <script type="module" src="assets/js/pages/login.js"></script>
</body>

</html>
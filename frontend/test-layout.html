<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试 - XIOS</title>
    <link rel="stylesheet" href="assets/css/theme.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <style>
        body {
            background-color: var(--bg-secondary);
            padding: 2rem;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 3rem;
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        .badge-success {
            background-color: var(--success-100);
            color: var(--success-800);
            border: 1px solid var(--success-200);
        }
        .badge-error {
            background-color: var(--error-100);
            color: var(--error-800);
            border: 1px solid var(--error-200);
        }
        .badge-warning {
            background-color: var(--warning-100);
            color: var(--warning-800);
            border: 1px solid var(--warning-200);
        }
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
        }
        .btn-primary {
            background-color: var(--primary-600);
            color: white;
        }
        .btn-primary:hover {
            background-color: var(--primary-700);
        }
        .btn-secondary {
            background-color: var(--secondary-600);
            color: white;
        }
        .btn-error {
            background-color: var(--error-600);
            color: white;
        }
        .btn-ghost {
            background-color: transparent;
            color: var(--text-secondary);
            border-color: var(--border-primary);
        }
        .btn-outline {
            background-color: transparent;
            color: var(--primary-600);
            border-color: var(--primary-600);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 2rem; color: var(--text-primary);">布局修复测试</h1>
        
        <!-- 重签记录示例 -->
        <div class="section">
            <h2 class="section-title">重签记录 - 修复后</h2>
            
            <!-- 成功记录 -->
            <div class="card record-card status-success mb-4">
                <div class="card-body">
                    <!-- 记录头部 -->
                    <div class="record-header">
                        <!-- 应用图标 -->
                        <div class="record-icon">
                            <span class="text-2xl">📱</span>
                        </div>

                        <!-- 应用信息 -->
                        <div class="record-info">
                            <h4 class="record-title">GETIDFA</h4>
                            <p class="record-subtitle">com.xxapp.getidfa</p>
                        </div>

                        <!-- 状态标签 -->
                        <div class="record-status">
                            <span class="badge badge-success">成功</span>
                        </div>
                    </div>

                    <!-- 详细信息网格 -->
                    <div class="record-meta">
                        <div><strong>原文件:</strong> <span>GETIDFA.ipa</span></div>
                        <div><strong>文件大小:</strong> <span>15.09 MB</span></div>
                        <div><strong>重签时间:</strong> <span>2025-08-22 13:37:10</span></div>
                        <div><strong>新Bundle ID:</strong> <span>保持原有</span></div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="record-actions">
                        <button class="btn btn-primary btn-sm">📥 下载重签IPA</button>
                        <a href="#" class="btn btn-secondary btn-sm">🌐 安装页面</a>
                        <button class="btn btn-ghost btn-sm">📱 二维码</button>
                        <button class="btn btn-outline btn-sm">📋 复制链接</button>
                        <button class="btn btn-error btn-sm">🗑️ 删除记录</button>
                    </div>
                </div>
            </div>

            <!-- 失败记录 -->
            <div class="card record-card status-error mb-4">
                <div class="card-body">
                    <!-- 记录头部 -->
                    <div class="record-header">
                        <!-- 应用图标 -->
                        <div class="record-icon">
                            <span class="text-2xl">📱</span>
                        </div>

                        <!-- 应用信息 -->
                        <div class="record-info">
                            <h4 class="record-title">TestApp</h4>
                            <p class="record-subtitle">com.example.testapp</p>
                        </div>

                        <!-- 状态标签 -->
                        <div class="record-status">
                            <span class="badge badge-error">失败</span>
                        </div>
                    </div>

                    <!-- 详细信息网格 -->
                    <div class="record-meta">
                        <div><strong>原文件:</strong> <span>TestApp.ipa</span></div>
                        <div><strong>文件大小:</strong> <span>25.3 MB</span></div>
                        <div><strong>重签时间:</strong> <span>2025-08-22 14:15:30</span></div>
                        <div><strong>新Bundle ID:</strong> <span>com.custom.testapp</span></div>
                    </div>

                    <!-- 错误信息 -->
                    <div style="margin-top: 1rem; padding: 1rem; background-color: var(--error-50); border: 1px solid var(--error-200); border-radius: 0.5rem;">
                        <strong style="color: var(--error-700);">❌ 重签失败:</strong>
                        <span style="color: var(--error-600);">证书已过期，请更新证书后重试</span>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="record-actions">
                        <button class="btn btn-error btn-sm">🗑️ 删除记录</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 上传记录示例 -->
        <div class="section">
            <h2 class="section-title">上传记录 - 修复后</h2>
            
            <!-- 成功记录 -->
            <div class="card record-card status-completed mb-4">
                <div class="card-body">
                    <!-- 记录头部 -->
                    <div class="record-header">
                        <!-- 应用图标 -->
                        <div class="record-icon">
                            <span class="text-2xl">📱</span>
                        </div>

                        <!-- 应用信息 -->
                        <div class="record-info">
                            <h4 class="record-title">GETIDFA</h4>
                            <p class="record-subtitle">com.xxapp.getidfa</p>
                        </div>

                        <!-- 状态标签 -->
                        <div class="record-status">
                            <span class="badge badge-success">成功</span>
                        </div>
                    </div>

                    <!-- 详细信息网格 -->
                    <div class="record-meta">
                        <div><strong>版本:</strong> <span>2.0</span></div>
                        <div><strong>大小:</strong> <span>14.26 MB</span></div>
                        <div><strong>上传时间:</strong> <span>2025-08-21 13:08:44</span></div>
                        <div><strong>认证方式:</strong> <span>Apple ID</span></div>
                        <div><strong>IP地址:</strong> <span>**************</span></div>
                        <div><strong>用户:</strong> <span>admin</span></div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="record-actions">
                        <a href="#" class="btn btn-primary btn-sm">🚀 TestFlight 链接</a>
                        <button class="btn btn-error btn-sm">🗑️ 删除记录</button>
                    </div>
                </div>
            </div>

            <!-- 失败记录 -->
            <div class="card record-card status-failed mb-4">
                <div class="card-body">
                    <!-- 记录头部 -->
                    <div class="record-header">
                        <!-- 应用图标 -->
                        <div class="record-icon">
                            <span class="text-2xl">📱</span>
                        </div>

                        <!-- 应用信息 -->
                        <div class="record-info">
                            <h4 class="record-title">FailedApp</h4>
                            <p class="record-subtitle">com.example.failedapp</p>
                        </div>

                        <!-- 状态标签 -->
                        <div class="record-status">
                            <span class="badge badge-error">失败</span>
                        </div>
                    </div>

                    <!-- 详细信息网格 -->
                    <div class="record-meta">
                        <div><strong>版本:</strong> <span>1.0</span></div>
                        <div><strong>大小:</strong> <span>18.5 MB</span></div>
                        <div><strong>上传时间:</strong> <span>2025-08-21 15:30:12</span></div>
                        <div><strong>认证方式:</strong> <span>API Key</span></div>
                    </div>

                    <!-- 错误信息 -->
                    <div style="margin-top: 1rem; padding: 1rem; background-color: var(--error-50); border: 1px solid var(--error-200); border-radius: 0.5rem;">
                        <div style="margin-bottom: 0.5rem;">
                            <strong style="color: var(--error-700);">❌ 版本号重复:</strong>
                            <span style="color: var(--error-600);">当前版本号已存在，之前上传的版本号为：2</span>
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <strong style="color: var(--error-700);">💡 解决方案:</strong>
                            <span style="color: var(--error-600);">请将Bundle Version (Build号)设置为大于 2 的数字后重新上传</span>
                        </div>
                        <details style="margin-top: 0.5rem;">
                            <summary style="cursor: pointer; color: var(--error-700);">🔍 查看详细信息</summary>
                            <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: var(--gray-100); border-radius: 0.25rem; font-size: 0.75rem; overflow-x: auto;">详细错误信息...</pre>
                        </details>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="record-actions">
                        <button class="btn btn-error btn-sm">🗑️ 删除记录</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

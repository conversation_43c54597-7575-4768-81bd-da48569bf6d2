<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XIOS - 主题演示</title>

    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/form.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
    <link rel="stylesheet" href="assets/css/theme.css">

    <style>
        body {
            background: var(--gradient-hero);
            min-height: 100vh;
            padding: var(--space-8);
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes float {

            0%,
            100% {
                transform: translate(0, 0) rotate(0deg);
            }

            33% {
                transform: translate(30px, -30px) rotate(120deg);
            }

            66% {
                transform: translate(-20px, 20px) rotate(240deg);
            }
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .demo-header {
            text-align: center;
            margin-bottom: var(--space-12);
            color: var(--text-inverse);
        }

        .demo-title {
            font-size: var(--text-5xl);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-4);
            background: linear-gradient(45deg, #fff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-subtitle {
            font-size: var(--text-xl);
            opacity: 0.9;
        }

        .demo-section {
            margin-bottom: var(--space-12);
        }

        .demo-section-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-semibold);
            margin-bottom: var(--space-6);
            color: var(--text-inverse);
            text-align: center;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-6);
        }

        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-4);
            justify-content: center;
        }

        .demo-card-content {
            padding: var(--space-6);
        }

        .back-link {
            position: fixed;
            top: var(--space-6);
            left: var(--space-6);
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }

            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }

            .demo-title {
                font-size: var(--text-3xl);
            }
        }
    </style>
</head>

<body>
    <!-- 返回链接 -->
    <a href="index.html" class="btn btn-glass back-link">
        ← 返回主页
    </a>

    <div class="demo-container">
        <!-- 头部 -->
        <div class="demo-header">
            <h1 class="demo-title">🎨 XIOS 主题演示</h1>
            <p class="demo-subtitle">体验全新的现代化设计系统</p>
        </div>

        <!-- 按钮演示 -->
        <div class="demo-section">
            <h2 class="demo-section-title">✨ 按钮组件</h2>
            <div class="demo-buttons">
                <button class="btn btn-primary">主要按钮</button>
                <button class="btn btn-secondary">次要按钮</button>
                <button class="btn btn-accent">强调按钮</button>
                <button class="btn btn-purple">紫色按钮</button>
                <button class="btn btn-pink">粉色按钮</button>
                <button class="btn btn-rainbow">彩虹按钮</button>
                <button class="btn btn-glass">玻璃按钮</button>
                <button class="btn btn-success">成功按钮</button>
                <button class="btn btn-warning">警告按钮</button>
                <button class="btn btn-error">错误按钮</button>
            </div>
        </div>

        <!-- 卡片演示 -->
        <div class="demo-section">
            <h2 class="demo-section-title">🃏 卡片组件</h2>
            <div class="demo-grid">
                <div class="card">
                    <div class="demo-card-content">
                        <h3>标准卡片</h3>
                        <p>这是一个标准的卡片组件，具有悬停效果和渐变顶部边框。</p>
                    </div>
                </div>

                <div class="card card-gradient-primary">
                    <div class="demo-card-content">
                        <h3>渐变卡片</h3>
                        <p>使用主色调渐变背景的卡片，适合突出重要内容。</p>
                    </div>
                </div>

                <div class="card card-glass">
                    <div class="demo-card-content">
                        <h3>玻璃态卡片</h3>
                        <p>具有毛玻璃效果的现代化卡片设计。</p>
                    </div>
                </div>

                <div class="card card-glow">
                    <div class="demo-card-content">
                        <h3>发光卡片</h3>
                        <p>带有发光效果的卡片，吸引用户注意力。</p>
                    </div>
                </div>

                <div class="card card-rainbow">
                    <div class="demo-card-content">
                        <h3>彩虹边框卡片</h3>
                        <p>具有动态彩虹边框的特殊卡片效果。</p>
                    </div>
                </div>

                <div class="card card-floating">
                    <div class="demo-card-content">
                        <h3>悬浮卡片</h3>
                        <p>始终保持悬浮状态的卡片，营造层次感。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 颜色演示 -->
        <div class="demo-section">
            <h2 class="demo-section-title">🌈 配色方案</h2>
            <div class="demo-grid">
                <div class="card card-gradient-primary">
                    <div class="demo-card-content">
                        <h3>主色调</h3>
                        <p>现代蓝紫渐变，科技感十足</p>
                    </div>
                </div>

                <div class="card card-gradient-accent">
                    <div class="demo-card-content">
                        <h3>强调色</h3>
                        <p>清新青色，活力四射</p>
                    </div>
                </div>

                <div class="card card-gradient-purple">
                    <div class="demo-card-content">
                        <h3>紫色系</h3>
                        <p>神秘优雅，高端大气</p>
                    </div>
                </div>

                <div class="card card-gradient-pink">
                    <div class="demo-card-content">
                        <h3>粉色系</h3>
                        <p>温暖可爱，充满活力</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 特效演示 -->
        <div class="demo-section">
            <h2 class="demo-section-title">✨ 特殊效果</h2>
            <div class="demo-buttons">
                <button class="btn btn-primary btn-lg">大尺寸按钮</button>
                <button class="btn btn-accent btn-sm">小尺寸按钮</button>
                <button class="btn btn-rainbow btn-xl">超大彩虹按钮</button>
                <button class="btn btn-glass btn-lg">大玻璃按钮</button>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="demo-section">
            <div class="card card-glass">
                <div class="demo-card-content" style="text-align: center;">
                    <h3>🎉 主题特色</h3>
                    <p>• 现代渐变配色方案</p>
                    <p>• 玻璃态毛玻璃效果</p>
                    <p>• 动态悬停交互</p>
                    <p>• 彩虹动画效果</p>
                    <p>• 响应式设计</p>
                    <p>• 无障碍支持</p>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
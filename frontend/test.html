<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XIOS - 功能测试</title>
    
    <!-- CSS 样式文件 -->
    <link rel="stylesheet" href="assets/css/base/variables.css">
    <link rel="stylesheet" href="assets/css/base/reset.css">
    <link rel="stylesheet" href="assets/css/base/typography.css">
    <link rel="stylesheet" href="assets/css/components/button.css">
    <link rel="stylesheet" href="assets/css/components/card.css">
    <link rel="stylesheet" href="assets/css/components/form.css">
    <link rel="stylesheet" href="assets/css/components/navbar.css">
    <link rel="stylesheet" href="assets/css/components/modal.css">
    <link rel="stylesheet" href="assets/css/layout/main.css">
</head>
<body>
    <div class="container py-8">
        <h1 class="text-center mb-8">🧪 XIOS 功能测试页面</h1>
        
        <!-- 组件测试 -->
        <div class="grid grid-cols-1 gap-8">
            <!-- 按钮测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">按钮组件测试</h2>
                </div>
                <div class="card-body">
                    <div class="flex gap-4 flex-wrap">
                        <button class="btn btn-primary">主要按钮</button>
                        <button class="btn btn-secondary">次要按钮</button>
                        <button class="btn btn-success">成功按钮</button>
                        <button class="btn btn-warning">警告按钮</button>
                        <button class="btn btn-error">错误按钮</button>
                        <button class="btn btn-outline">轮廓按钮</button>
                        <button class="btn btn-ghost">幽灵按钮</button>
                    </div>
                    <div class="flex gap-4 flex-wrap mt-4">
                        <button class="btn btn-primary btn-xs">超小</button>
                        <button class="btn btn-primary btn-sm">小</button>
                        <button class="btn btn-primary btn-md">中</button>
                        <button class="btn btn-primary btn-lg">大</button>
                        <button class="btn btn-primary btn-xl">超大</button>
                    </div>
                </div>
            </div>
            
            <!-- 卡片测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">卡片组件测试</h2>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="card card-elevated">
                            <div class="card-body">
                                <h3 class="font-semibold mb-2">提升卡片</h3>
                                <p class="text-sm text-secondary">这是一个带阴影的卡片</p>
                            </div>
                        </div>
                        <div class="card card-outlined">
                            <div class="card-body">
                                <h3 class="font-semibold mb-2">轮廓卡片</h3>
                                <p class="text-sm text-secondary">这是一个带边框的卡片</p>
                            </div>
                        </div>
                        <div class="card card-filled">
                            <div class="card-body">
                                <h3 class="font-semibold mb-2">填充卡片</h3>
                                <p class="text-sm text-secondary">这是一个带背景的卡片</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 表单测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">表单组件测试</h2>
                </div>
                <div class="card-body">
                    <form class="form">
                        <div class="form-group">
                            <label class="form-label form-label-required" for="test-input">文本输入</label>
                            <input type="text" id="test-input" class="form-control" placeholder="请输入文本">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="test-select">选择框</label>
                            <select id="test-select" class="form-control form-select">
                                <option value="">请选择</option>
                                <option value="1">选项1</option>
                                <option value="2">选项2</option>
                                <option value="3">选项3</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="test-textarea">文本域</label>
                            <textarea id="test-textarea" class="form-control form-textarea" placeholder="请输入多行文本"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">复选框</label>
                            <div class="form-checkbox-group">
                                <div class="form-checkbox-item">
                                    <input type="checkbox" id="check1" class="form-checkbox">
                                    <label for="check1">选项1</label>
                                </div>
                                <div class="form-checkbox-item">
                                    <input type="checkbox" id="check2" class="form-checkbox">
                                    <label for="check2">选项2</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">单选框</label>
                            <div class="form-radio-group">
                                <div class="form-radio-item">
                                    <input type="radio" id="radio1" name="radio-test" class="form-radio">
                                    <label for="radio1">选项A</label>
                                </div>
                                <div class="form-radio-item">
                                    <input type="radio" id="radio2" name="radio-test" class="form-radio">
                                    <label for="radio2">选项B</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="test-file">文件上传</label>
                            <div class="form-file">
                                <input type="file" id="test-file" accept=".ipa">
                                <label for="test-file" class="form-file-label">
                                    <div class="form-file-icon">📁</div>
                                    <div class="form-file-text">点击选择文件</div>
                                    <div class="form-file-hint">支持.ipa格式</div>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">提交</button>
                            <button type="reset" class="btn btn-secondary">重置</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 工具类测试 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-header-title">工具类测试</h2>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold mb-2">徽章</h3>
                        <div class="flex gap-2 flex-wrap">
                            <span class="badge badge-primary">主要</span>
                            <span class="badge badge-secondary">次要</span>
                            <span class="badge badge-success">成功</span>
                            <span class="badge badge-warning">警告</span>
                            <span class="badge badge-error">错误</span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-lg font-semibold mb-2">进度条</h3>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 30%"></div>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 90%"></div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-semibold mb-2">文本样式</h3>
                        <p class="text-primary">主要文本</p>
                        <p class="text-secondary">次要文本</p>
                        <p class="text-muted">静音文本</p>
                        <p class="text-success">成功文本</p>
                        <p class="text-warning">警告文本</p>
                        <p class="text-error">错误文本</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能测试按钮 -->
        <div class="text-center mt-8">
            <a href="index.html" class="btn btn-primary btn-lg">
                <span>🚀</span>
                <span>进入主应用</span>
            </a>
        </div>
    </div>
    
    <script>
        // 简单的交互测试
        document.addEventListener('DOMContentLoaded', function() {
            // 表单提交测试
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('✅ 表单提交测试成功！');
            });
            
            // 文件上传测试
            const fileInput = document.getElementById('test-file');
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const label = document.querySelector('.form-file-label .form-file-text');
                    label.textContent = `已选择: ${file.name}`;
                    document.querySelector('.form-file').classList.add('has-file');
                }
            });
            
            console.log('🧪 测试页面加载完成');
        });
    </script>
</body>
</html>

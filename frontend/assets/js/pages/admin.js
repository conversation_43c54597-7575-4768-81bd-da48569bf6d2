/**
 * 管理后台页面模块
 * 处理用户管理和系统管理功能
 */

import api from '../core/api.js';
import auth from '../core/auth.js';
import { createCard, createStatsCard } from '../components/Card.js';
import { createButton } from '../components/Button.js';
import { formatDateTime } from '../core/utils.js';

class AdminPage {
  constructor() {
    this.container = null;
    this.isInitialized = false;
    
    this.init();
  }

  /**
   * 初始化页面
   */
  init() {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  /**
   * 设置页面
   */
  setup() {
    this.container = document.getElementById('admin-content-container');
    
    if (!this.container) {
      console.error('Admin page container not found');
      return;
    }
    
    // 检查管理员权限
    if (!auth.isAdmin()) {
      this.showAccessDenied();
      return;
    }
    
    this.createAdminInterface();
    this.isInitialized = true;
    
    // 监听路由变化
    window.addEventListener('router:navigated', (e) => {
      if (e.detail.route.path === '/admin') {
        this.loadDashboardData();
      }
    });
  }

  /**
   * 显示访问拒绝页面
   */
  showAccessDenied() {
    this.container.innerHTML = `
      <div class="text-center py-16">
        <div class="text-6xl mb-4">🚫</div>
        <h2 class="text-2xl font-bold mb-4">访问被拒绝</h2>
        <p class="text-secondary mb-8">您没有权限访问管理后台</p>
        <a href="#/" class="btn btn-primary">返回首页</a>
      </div>
    `;
  }

  /**
   * 创建管理界面
   */
  createAdminInterface() {
    const adminHTML = `
      <div class="admin-dashboard">
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" id="stats-container">
          <!-- 统计卡片将由JavaScript动态生成 -->
        </div>
        
        <!-- 管理功能标签页 -->
        <div class="card">
          <div class="card-header">
            <div class="navbar-tabs">
              <div class="navbar-tabs-container">
                <ul class="navbar-tabs-nav">
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link active" data-tab="users">
                      👥 用户管理
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="uploads">
                      📤 上传管理
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="resigns">
                      🔐 重签管理
                    </a>
                  </li>
                  <li class="navbar-tabs-item">
                    <a href="#" class="navbar-tabs-link" data-tab="system">
                      ⚙️ 系统设置
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="card-body">
            <!-- 用户管理 -->
            <div id="users-tab" class="admin-tab active">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">用户管理</h3>
                <button class="btn btn-primary" id="add-user-btn">
                  <span>➕</span>
                  <span>添加用户</span>
                </button>
              </div>
              <div id="users-list">
                <div class="text-center py-8">加载中...</div>
              </div>
            </div>
            
            <!-- 上传管理 -->
            <div id="uploads-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">上传管理</h3>
                <button class="btn btn-secondary" id="refresh-uploads-btn">
                  <span>🔄</span>
                  <span>刷新</span>
                </button>
              </div>
              <div id="uploads-list">
                <div class="text-center py-8">加载中...</div>
              </div>
            </div>
            
            <!-- 重签管理 -->
            <div id="resigns-tab" class="admin-tab" style="display: none;">
              <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold">重签管理</h3>
                <button class="btn btn-secondary" id="refresh-resigns-btn">
                  <span>🔄</span>
                  <span>刷新</span>
                </button>
              </div>
              <div id="resigns-list">
                <div class="text-center py-8">加载中...</div>
              </div>
            </div>
            
            <!-- 系统设置 -->
            <div id="system-tab" class="admin-tab" style="display: none;">
              <h3 class="text-lg font-semibold mb-6">系统设置</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="card card-filled">
                  <div class="card-body">
                    <h4 class="font-medium mb-3">系统信息</h4>
                    <div id="system-info">
                      <div class="text-center py-4">加载中...</div>
                    </div>
                  </div>
                </div>
                
                <div class="card card-filled">
                  <div class="card-body">
                    <h4 class="font-medium mb-3">系统操作</h4>
                    <div class="space-y-3">
                      <button class="btn btn-warning btn-block" id="clear-cache-btn">
                        🗑️ 清理缓存
                      </button>
                      <button class="btn btn-secondary btn-block" id="backup-db-btn">
                        💾 备份数据库
                      </button>
                      <button class="btn btn-error btn-block" id="restart-service-btn">
                        🔄 重启服务
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    this.container.innerHTML = adminHTML;
    this.setupTabSwitching();
    this.setupEventListeners();
  }

  /**
   * 设置标签页切换
   */
  setupTabSwitching() {
    const tabLinks = document.querySelectorAll('[data-tab]');
    const tabContents = document.querySelectorAll('.admin-tab');

    tabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        const targetTab = link.getAttribute('data-tab');
        
        // 更新标签页状态
        tabLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // 显示对应内容
        tabContents.forEach(content => {
          content.style.display = 'none';
        });
        
        const targetContent = document.getElementById(`${targetTab}-tab`);
        if (targetContent) {
          targetContent.style.display = 'block';
        }
        
        // 加载对应数据
        this.loadTabData(targetTab);
      });
    });
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 添加用户按钮
    const addUserBtn = document.getElementById('add-user-btn');
    if (addUserBtn) {
      addUserBtn.addEventListener('click', () => this.showAddUserModal());
    }
    
    // 刷新按钮
    const refreshUploadsBtn = document.getElementById('refresh-uploads-btn');
    if (refreshUploadsBtn) {
      refreshUploadsBtn.addEventListener('click', () => this.loadUploads());
    }
    
    const refreshResignsBtn = document.getElementById('refresh-resigns-btn');
    if (refreshResignsBtn) {
      refreshResignsBtn.addEventListener('click', () => this.loadResigns());
    }
    
    // 系统操作按钮
    const clearCacheBtn = document.getElementById('clear-cache-btn');
    if (clearCacheBtn) {
      clearCacheBtn.addEventListener('click', () => this.clearCache());
    }
    
    const backupDbBtn = document.getElementById('backup-db-btn');
    if (backupDbBtn) {
      backupDbBtn.addEventListener('click', () => this.backupDatabase());
    }
    
    const restartServiceBtn = document.getElementById('restart-service-btn');
    if (restartServiceBtn) {
      restartServiceBtn.addEventListener('click', () => this.restartService());
    }
  }

  /**
   * 加载仪表板数据
   */
  async loadDashboardData() {
    try {
      const response = await api.get('/api/admin/dashboard');

      if (response.data.success) {
        this.renderStats(response.data.stats);
      } else {
        // 使用默认统计数据
        this.renderStats({
          total_users: 0,
          today_uploads: 0,
          today_resigns: 0,
          system_status: '未知'
        });
      }
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      // 使用默认统计数据
      this.renderStats({
        total_users: 0,
        today_uploads: 0,
        today_resigns: 0,
        system_status: '连接失败'
      });
    }

    // 默认加载用户列表
    this.loadUsers();
  }

  /**
   * 渲染统计卡片
   */
  renderStats(stats) {
    const statsContainer = document.getElementById('stats-container');
    if (!statsContainer) return;
    
    statsContainer.innerHTML = '';
    
    // 创建统计卡片
    const cards = [
      { label: '总用户数', value: stats.total_users || 0, icon: '👥' },
      { label: '今日上传', value: stats.today_uploads || 0, icon: '📤' },
      { label: '今日重签', value: stats.today_resigns || 0, icon: '🔐' },
      { label: '系统状态', value: stats.system_status || '正常', icon: '⚙️' }
    ];
    
    cards.forEach(cardData => {
      const card = createStatsCard({
        value: cardData.value,
        label: cardData.label,
        variant: 'elevated'
      });
      
      card.mount(statsContainer);
    });
  }

  /**
   * 加载标签页数据
   */
  loadTabData(tab) {
    switch (tab) {
      case 'users':
        this.loadUsers();
        break;
      case 'uploads':
        this.loadUploads();
        break;
      case 'resigns':
        this.loadResigns();
        break;
      case 'system':
        this.loadSystemInfo();
        break;
    }
  }

  /**
   * 加载用户列表
   */
  async loadUsers() {
    const usersList = document.getElementById('users-list');
    if (!usersList) return;

    try {
      usersList.innerHTML = '<div class="text-center py-8">加载中...</div>';

      const response = await api.get('/api/admin/users');

      if (response.data.success && response.data.users) {
        this.renderUsers(response.data.users);
      } else {
        usersList.innerHTML = '<div class="text-center py-8 text-muted">暂无用户数据</div>';
      }
    } catch (error) {
      console.error('Failed to load users:', error);

      if (error.status === 401) {
        usersList.innerHTML = '<div class="text-center py-8 text-error">认证失败，请重新登录</div>';
      } else if (error.status === 403) {
        usersList.innerHTML = '<div class="text-center py-8 text-error">权限不足，需要管理员权限</div>';
      } else {
        usersList.innerHTML = '<div class="text-center py-8 text-error">加载失败，请稍后重试</div>';
      }
    }
  }

  /**
   * 渲染用户列表
   */
  renderUsers(users) {
    const usersList = document.getElementById('users-list');
    if (!usersList) return;

    if (!users || users.length === 0) {
      usersList.innerHTML = '<div class="text-center py-8 text-muted">暂无用户</div>';
      return;
    }

    const usersHTML = users.map(user => {
      // 处理时间显示
      const createdAt = user.created_at.$date || user.created_at;
      const timeDisplay = formatDateTime(createdAt);

      return `
        <div class="card mb-4">
          <div class="card-body">
            <div class="flex justify-between items-start">
              <div>
                <h4 class="font-medium">${user.username}</h4>
                <p class="text-sm text-secondary">${user.email || ''}</p>
                <div class="flex gap-4 mt-2 text-sm">
                  <span><strong>角色:</strong> ${user.role === 'admin' ? '管理员' : '普通用户'}</span>
                  <span><strong>状态:</strong> ${user.is_expired ? '已过期' : '正常'}</span>
                  <span><strong>注册时间:</strong> ${timeDisplay}</span>
                </div>
                ${user.last_login ? `
                  <div class="mt-1 text-sm text-secondary">
                    <strong>最后登录:</strong> ${formatDateTime(user.last_login.$date || user.last_login)}
                  </div>
                ` : ''}
              </div>
              <div class="flex gap-2">
                <button class="btn btn-secondary btn-sm" onclick="window.adminPage.editUser('${user._id || user.id}')">
                  ✏️ 编辑
                </button>
                <button class="btn btn-warning btn-sm" onclick="window.adminPage.toggleUserStatus('${user._id || user.id}', ${user.is_expired})">
                  ${user.is_expired ? '🔓 激活' : '🔒 禁用'}
                </button>
                <button class="btn btn-error btn-sm" onclick="window.adminPage.deleteUser('${user._id || user.id}', '${user.username}')">
                  🗑️ 删除
                </button>
              </div>
            </div>
          </div>
        </div>
      `;
    }).join('');

    usersList.innerHTML = usersHTML;

    // 将页面实例绑定到window，供按钮调用
    window.adminPage = this;
  }

  /**
   * 加载上传记录
   */
  async loadUploads() {
    const uploadsList = document.getElementById('uploads-list');
    if (!uploadsList) return;
    
    try {
      uploadsList.innerHTML = '<div class="text-center py-8">加载中...</div>';
      
      const response = await api.get('/api/admin/uploads');
      
      if (response.data.success) {
        // 这里可以渲染上传记录
        uploadsList.innerHTML = '<div class="text-center py-8 text-muted">功能开发中...</div>';
      }
    } catch (error) {
      console.error('Failed to load uploads:', error);
      uploadsList.innerHTML = '<div class="text-center py-8 text-error">加载失败</div>';
    }
  }

  /**
   * 加载重签记录
   */
  async loadResigns() {
    const resignsList = document.getElementById('resigns-list');
    if (!resignsList) return;
    
    try {
      resignsList.innerHTML = '<div class="text-center py-8">加载中...</div>';
      
      const response = await api.get('/api/admin/resigns');
      
      if (response.data.success) {
        // 这里可以渲染重签记录
        resignsList.innerHTML = '<div class="text-center py-8 text-muted">功能开发中...</div>';
      }
    } catch (error) {
      console.error('Failed to load resigns:', error);
      resignsList.innerHTML = '<div class="text-center py-8 text-error">加载失败</div>';
    }
  }

  /**
   * 加载系统信息
   */
  async loadSystemInfo() {
    const systemInfo = document.getElementById('system-info');
    if (!systemInfo) return;

    try {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 600));

      // 模拟系统信息
      const mockInfo = {
        server_time: new Date().toISOString(),
        uptime: '15天 8小时 32分钟',
        version: '2.1.0',
        database: '正常运行'
      };

      systemInfo.innerHTML = `
        <div class="space-y-2 text-sm">
          <div><strong>服务器时间:</strong> ${formatDateTime(mockInfo.server_time)}</div>
          <div><strong>运行时间:</strong> ${mockInfo.uptime}</div>
          <div><strong>版本:</strong> ${mockInfo.version}</div>
          <div><strong>数据库:</strong> ${mockInfo.database}</div>
          <div><strong>CPU使用率:</strong> 15.2%</div>
          <div><strong>内存使用:</strong> 2.1GB / 8GB</div>
          <div><strong>磁盘空间:</strong> 45GB / 100GB</div>
        </div>
      `;
    } catch (error) {
      console.error('Failed to load system info:', error);
      systemInfo.innerHTML = '<div class="text-error">加载失败</div>';
    }
  }

  /**
   * 显示添加用户模态框
   */
  showAddUserModal() {
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-backdrop active"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">添加新用户</h3>
          <button class="modal-close" onclick="this.closest('.modal').remove()">×</button>
        </div>
        <div class="modal-body">
          <form id="add-user-form">
            <div class="form-group">
              <label class="form-label form-label-required">用户名</label>
              <input type="text" name="username" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">邮箱</label>
              <input type="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">密码</label>
              <input type="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
              <label class="form-label">角色</label>
              <select name="role" class="form-control">
                <option value="user">普通用户</option>
                <option value="admin">管理员</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitAddUser()">添加用户</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.querySelector('.modal-backdrop').addEventListener('click', () => {
      modal.remove();
    });
  }

  /**
   * 提交添加用户
   */
  async submitAddUser() {
    const form = document.getElementById('add-user-form');
    const formData = new FormData(form);

    const userData = {
      username: formData.get('username'),
      email: formData.get('email'),
      password: formData.get('password'),
      role: formData.get('role')
    };

    try {
      const response = await api.post('/api/admin/users', userData);

      if (response.data.success) {
        alert('✅ 用户添加成功！');
        document.querySelector('.modal').remove();
        this.loadUsers();
      } else {
        alert('❌ 添加失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to add user:', error);
      alert('❌ 添加失败：' + error.message);
    }
  }

  /**
   * 编辑用户
   */
  async editUser(userId) {
    try {
      const response = await api.get(`/api/admin/users/${userId}`);

      if (response.data.success) {
        const user = response.data.user;
        this.showEditUserModal(user);
      } else {
        alert('❌ 获取用户信息失败');
      }
    } catch (error) {
      console.error('Failed to get user:', error);
      alert('❌ 获取用户信息失败：' + error.message);
    }
  }

  /**
   * 显示编辑用户模态框
   */
  showEditUserModal(user) {
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.innerHTML = `
      <div class="modal-backdrop active"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">编辑用户</h3>
          <button class="modal-close" onclick="this.closest('.modal').remove()">×</button>
        </div>
        <div class="modal-body">
          <form id="edit-user-form">
            <input type="hidden" name="user_id" value="${user._id || user.id}">
            <div class="form-group">
              <label class="form-label form-label-required">用户名</label>
              <input type="text" name="username" class="form-control" value="${user.username}" required>
            </div>
            <div class="form-group">
              <label class="form-label form-label-required">邮箱</label>
              <input type="email" name="email" class="form-control" value="${user.email || ''}" required>
            </div>
            <div class="form-group">
              <label class="form-label">新密码（留空则不修改）</label>
              <input type="password" name="password" class="form-control">
            </div>
            <div class="form-group">
              <label class="form-label">角色</label>
              <select name="role" class="form-control">
                <option value="user" ${user.role === 'user' ? 'selected' : ''}>普通用户</option>
                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>管理员</option>
              </select>
            </div>
            <div class="form-group">
              <div class="form-checkbox-item">
                <input type="checkbox" name="is_expired" ${user.is_expired ? 'checked' : ''}>
                <label>账号已过期</label>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">取消</button>
          <button class="btn btn-primary" onclick="window.adminPage.submitEditUser()">保存修改</button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.querySelector('.modal-backdrop').addEventListener('click', () => {
      modal.remove();
    });
  }

  /**
   * 提交编辑用户
   */
  async submitEditUser() {
    const form = document.getElementById('edit-user-form');
    const formData = new FormData(form);

    const userData = {
      username: formData.get('username'),
      email: formData.get('email'),
      role: formData.get('role'),
      is_expired: formData.get('is_expired') === 'on'
    };

    // 只有填写了密码才包含密码字段
    const password = formData.get('password');
    if (password) {
      userData.password = password;
    }

    const userId = formData.get('user_id');

    try {
      const response = await api.put(`/api/admin/users/${userId}`, userData);

      if (response.data.success) {
        alert('✅ 用户信息更新成功！');
        document.querySelector('.modal').remove();
        this.loadUsers();
      } else {
        alert('❌ 更新失败：' + (response.data.message || '未知错误'));
      }
    } catch (error) {
      console.error('Failed to update user:', error);
      alert('❌ 更新失败：' + error.message);
    }
  }

  /**
   * 切换用户状态
   */
  async toggleUserStatus(userId, currentExpired) {
    const action = currentExpired ? '激活' : '禁用';

    if (confirm(`确定要${action}该用户吗？`)) {
      try {
        const response = await api.put(`/api/admin/users/${userId}`, {
          is_expired: !currentExpired
        });

        if (response.data.success) {
          alert(`✅ 用户已${action}！`);
          this.loadUsers();
        } else {
          alert(`❌ ${action}失败：` + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error(`Failed to toggle user status:`, error);
        alert(`❌ ${action}失败：` + error.message);
      }
    }
  }

  /**
   * 删除用户
   */
  async deleteUser(userId, username) {
    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
      try {
        const response = await api.delete(`/api/admin/users/${userId}`);

        if (response.data.success) {
          alert('✅ 用户已删除！');
          this.loadUsers();
        } else {
          alert('❌ 删除失败：' + (response.data.message || '未知错误'));
        }
      } catch (error) {
        console.error('Failed to delete user:', error);
        alert('❌ 删除失败：' + error.message);
      }
    }
  }

  /**
   * 清理缓存
   */
  async clearCache() {
    if (confirm('确定要清理系统缓存吗？')) {
      try {
        const response = await api.post('/api/admin/clear-cache');
        if (response.data.success) {
          alert('✅ 缓存清理成功！');
        }
      } catch (error) {
        alert('❌ 缓存清理失败：' + error.message);
      }
    }
  }

  /**
   * 备份数据库
   */
  async backupDatabase() {
    if (confirm('确定要备份数据库吗？')) {
      try {
        const response = await api.post('/api/admin/backup-database');
        if (response.data.success) {
          alert('✅ 数据库备份成功！');
        }
      } catch (error) {
        alert('❌ 数据库备份失败：' + error.message);
      }
    }
  }

  /**
   * 重启服务
   */
  async restartService() {
    if (confirm('确定要重启服务吗？这将导致短暂的服务中断。')) {
      try {
        const response = await api.post('/api/admin/restart-service');
        if (response.data.success) {
          alert('✅ 服务重启请求已提交！');
        }
      } catch (error) {
        alert('❌ 服务重启失败：' + error.message);
      }
    }
  }
}

// 创建页面实例
new AdminPage();

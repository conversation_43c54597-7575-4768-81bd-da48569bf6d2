/**
 * 登录页面
 * 处理用户登录功能
 */

import api from '../core/api.js';
import auth from '../core/auth.js';

class LoginPage {
  constructor() {
    this.form = document.getElementById('login-form');
    this.errorMessage = document.getElementById('error-message');
    this.loadingOverlay = document.getElementById('loading-overlay');
    this.loginBtn = document.getElementById('login-btn');
    
    this.init();
  }
  
  /**
   * 初始化页面
   */
  init() {
    // 检查是否已登录
    this.checkAuthStatus();
    
    // 绑定表单提交事件
    this.form.addEventListener('submit', (e) => this.handleSubmit(e));
    
    // 绑定输入框事件
    const inputs = this.form.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', () => this.clearError());
    });
    
    // 监听认证事件
    window.addEventListener('auth:login', () => {
      this.redirectToApp();
    });
    
    // 绑定全局方法供HTML调用
    window.loginPage = this;
  }
  
  /**
   * 检查认证状态
   */
  async checkAuthStatus() {
    try {
      await auth.waitForInit();
      
      if (auth.isAuthenticated()) {
        this.redirectToApp();
      }
    } catch (error) {
      console.error('Auth check failed:', error);
    }
  }
  
  /**
   * 处理表单提交
   */
  async handleSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(this.form);
    const credentials = {
      username: formData.get('username'),
      password: formData.get('password'),
      remember: formData.get('remember') === 'on'
    };
    
    // 基本验证
    if (!this.validateForm(credentials)) {
      return;
    }
    
    try {
      this.showLoading(true);
      this.clearError();
      
      const result = await auth.login(credentials);
      
      if (result.success) {
        // 登录成功，重定向到应用
        this.redirectToApp();
      } else {
        // 检查是否是账号未激活或过期错误
        if (this.isActivationError(result.message)) {
          this.showActivationAlert(credentials.username, result.message);
        } else {
          this.showError(result.message || '登录失败，请检查用户名和密码');
        }
      }
      
    } catch (error) {
      console.error('Login failed:', error);
      this.handleLoginError(error);
    } finally {
      this.showLoading(false);
    }
  }
  
  /**
   * 表单验证
   */
  validateForm(credentials) {
    if (!credentials.username || !credentials.password) {
      this.showError('请输入用户名和密码');
      return false;
    }
    
    if (credentials.username.length < 3) {
      this.showError('用户名长度至少3位');
      return false;
    }
    
    if (credentials.password.length < 6) {
      this.showError('密码长度至少6位');
      return false;
    }
    
    return true;
  }
  
  /**
   * 检查是否是激活相关错误
   */
  isActivationError(message) {
    if (!message) return false;
    
    const activationKeywords = ['未激活', '过期', '激活码', 'expired', 'activation'];
    return activationKeywords.some(keyword => message.includes(keyword));
  }
  
  /**
   * 显示激活提示
   */
  showActivationAlert(username, errorMessage) {
    const isExpired = errorMessage.includes('过期') || errorMessage.includes('激活码');
    const title = isExpired ? '账号已过期' : '账号未激活';
    const description = '请使用激活码激活您的账号。';
    
    this.errorMessage.innerHTML = `
      <div style="background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px;">
        <div style="margin-bottom: 15px;">
          <strong>${title}</strong><br>
          ${description}
          ${errorMessage ? '<br><small style="color: #666; font-size: 12px;">详情: ' + errorMessage + '</small>' : ''}
        </div>
        <div style="display: flex; gap: 10px; justify-content: center;">
          <button onclick="window.loginPage.goToActivation('${username}')" class="btn btn-primary btn-sm">
            🔑 去激活账号
          </button>
          <button onclick="window.loginPage.clearError()" class="btn btn-secondary btn-sm">
            取消
          </button>
        </div>
      </div>
    `;
    this.errorMessage.style.display = 'block';
  }
  
  /**
   * 跳转到激活页面
   */
  goToActivation(username) {
    // 将用户名保存到sessionStorage，方便激活页面使用
    if (username) {
      sessionStorage.setItem('activation_username', username);
    }
    window.location.href = 'activate.html';
  }
  
  /**
   * 处理登录错误
   */
  handleLoginError(error) {
    let errorMessage = '登录失败，请检查网络连接';
    
    if (error.status === 401) {
      errorMessage = '用户名或密码错误';
    } else if (error.status === 403) {
      errorMessage = '账号已被禁用或已过期';
    } else if (error.status === 429) {
      errorMessage = '登录尝试过于频繁，请稍后再试';
    } else if (error.status === 500) {
      errorMessage = '服务器错误，请稍后再试';
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    this.showError(errorMessage);
  }
  
  /**
   * 显示错误消息
   */
  showError(message) {
    this.errorMessage.innerHTML = `<div style="color: #dc3545;">${message}</div>`;
    this.errorMessage.style.display = 'block';
  }
  
  /**
   * 清除错误消息
   */
  clearError() {
    this.errorMessage.style.display = 'none';
  }
  
  /**
   * 显示/隐藏加载状态
   */
  showLoading(show) {
    if (show) {
      this.loadingOverlay.style.display = 'flex';
      this.loginBtn.disabled = true;
    } else {
      this.loadingOverlay.style.display = 'none';
      this.loginBtn.disabled = false;
    }
  }
  
  /**
   * 重定向到应用
   */
  redirectToApp() {
    // 检查用户角色，管理员跳转到管理页面
    const user = auth.getUser();
    if (user && user.role === 'admin') {
      window.location.href = 'admin.html';
    } else {
      window.location.href = 'index.html';
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new LoginPage();
});

export default LoginPage;

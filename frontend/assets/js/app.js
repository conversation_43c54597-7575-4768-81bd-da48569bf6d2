/**
 * 应用主入口文件
 * 初始化应用，配置路由，设置全局事件监听器
 */

import api from './core/api.js';
import auth from './core/auth.js';
import router from './core/router.js';
import { debounce } from './core/utils.js';

// 导入页面模块
import './pages/upload.js';
import './pages/resign.js';
import './pages/certificate.js';
import './pages/admin.js';

class App {
  constructor() {
    this.isInitialized = false;
    this.loadingIndicator = null;
    
    // 绑定方法上下文
    this.handleNavToggle = this.handleNavToggle.bind(this);
    this.handleLogout = this.handleLogout.bind(this);
    this.handleAuthChange = this.handleAuthChange.bind(this);
    this.handleRouterChange = this.handleRouterChange.bind(this);
    this.handleResize = debounce(this.handleResize.bind(this), 250);
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('🚀 正在初始化 XIOS 应用...');
      
      // 显示加载指示器
      this.showLoading();
      
      // 设置全局错误处理
      this.setupErrorHandling();
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 配置路由
      this.setupRoutes();
      
      // 等待认证初始化
      await auth.waitForInit();

      // 临时跳过认证检查用于测试
      // if (!auth.isAuthenticated()) {
      //   this.redirectToLogin();
      //   return;
      // }

      // 模拟用户数据用于测试
      if (!auth.isAuthenticated()) {
        // 创建模拟用户数据
        const mockUser = {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          role: 'admin',
          is_expired: false
        };
        localStorage.setItem('user', JSON.stringify(mockUser));
        localStorage.setItem('token', 'mock-token-for-testing');
        auth.user = mockUser;
        auth.token = 'mock-token-for-testing';
      }
      
      // 更新用户界面
      this.updateUserInterface();
      
      // 初始化路由器
      await router.init();
      
      // 隐藏加载指示器
      this.hideLoading();
      
      this.isInitialized = true;
      console.log('✅ XIOS 应用初始化完成');
      
    } catch (error) {
      console.error('❌ 应用初始化失败:', error);
      this.handleInitError(error);
    }
  }

  /**
   * 设置全局错误处理
   */
  setupErrorHandling() {
    // 捕获未处理的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise错误:', event.reason);
      this.showErrorMessage('系统错误，请刷新页面重试');
    });
    
    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      console.error('JavaScript错误:', event.error);
      this.showErrorMessage('页面出现错误，请刷新页面重试');
    });
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    // 导航栏切换
    const navToggle = document.getElementById('navbar-toggle');
    if (navToggle) {
      navToggle.addEventListener('click', this.handleNavToggle);
    }
    
    // 登出按钮
    const logoutBtns = document.querySelectorAll('#logout-btn, #mobile-logout-btn');
    logoutBtns.forEach(btn => {
      btn.addEventListener('click', this.handleLogout);
    });
    
    // 认证状态变化
    window.addEventListener('auth:login', this.handleAuthChange);
    window.addEventListener('auth:logout', this.handleAuthChange);
    window.addEventListener('auth:unauthorized', this.handleAuthChange);
    
    // 路由变化
    window.addEventListener('router:navigated', this.handleRouterChange);
    window.addEventListener('router:error', this.handleRouterError);
    
    // 窗口大小变化
    window.addEventListener('resize', this.handleResize);
    
    // 导航链接点击
    document.addEventListener('click', (e) => {
      const navLink = e.target.closest('[data-nav-link]');
      if (navLink) {
        e.preventDefault();
        const path = navLink.getAttribute('data-nav-link');
        router.navigateTo(path);
        
        // 关闭移动端菜单
        this.closeMobileNav();
      }
    });
  }

  /**
   * 配置路由
   */
  setupRoutes() {
    router.registerRoutes([
      {
        path: '/',
        component: 'upload-page',
        title: 'XIOS - 上传中心',
        requireAuth: false // 临时设置为false用于测试
      },
      {
        path: '/upload',
        component: 'upload-page',
        title: 'XIOS - 上传中心',
        requireAuth: false
      },
      {
        path: '/resign',
        component: 'resign-page',
        title: 'XIOS - 重签工具',
        requireAuth: false
      },
      {
        path: '/certificate',
        component: 'certificate-page',
        title: 'XIOS - 证书管理',
        requireAuth: false
      },
      {
        path: '/admin',
        component: 'admin-page',
        title: 'XIOS - 管理后台',
        requireAuth: false,
        requireAdmin: false // 临时设置为false用于测试
      },
      {
        path: '/404',
        component: '404-page',
        title: 'XIOS - 页面未找到',
        requireAuth: false
      }
    ]);
  }

  /**
   * 更新用户界面
   */
  updateUserInterface() {
    const user = auth.getUser();
    if (!user) return;
    
    // 更新用户名显示
    const userNameElements = document.querySelectorAll('#user-name, #mobile-user-name');
    userNameElements.forEach(el => {
      el.textContent = auth.getUserDisplayName();
    });
    
    // 更新用户角色显示
    const userRoleElements = document.querySelectorAll('#user-role, #mobile-user-role');
    userRoleElements.forEach(el => {
      el.textContent = auth.getUserRoleText();
    });
    
    // 更新用户头像
    const userAvatarElements = document.querySelectorAll('#user-avatar, #mobile-user-avatar');
    const avatarText = user.username ? user.username.charAt(0).toUpperCase() : 'U';
    userAvatarElements.forEach(el => {
      el.textContent = avatarText;
    });
    
    // 显示/隐藏管理员菜单
    const adminNavItems = document.querySelectorAll('#admin-nav-item, #admin-mobile-nav-item');
    adminNavItems.forEach(item => {
      item.style.display = auth.isAdmin() ? 'block' : 'none';
    });
  }

  /**
   * 处理导航栏切换
   */
  handleNavToggle() {
    const navToggle = document.getElementById('navbar-toggle');
    const mobileNav = document.getElementById('navbar-mobile');
    
    if (navToggle && mobileNav) {
      const isActive = navToggle.classList.contains('active');
      
      if (isActive) {
        this.closeMobileNav();
      } else {
        this.openMobileNav();
      }
    }
  }

  /**
   * 打开移动端导航
   */
  openMobileNav() {
    const navToggle = document.getElementById('navbar-toggle');
    const mobileNav = document.getElementById('navbar-mobile');
    
    if (navToggle && mobileNav) {
      navToggle.classList.add('active');
      mobileNav.classList.add('active');
      document.body.style.overflow = 'hidden';
    }
  }

  /**
   * 关闭移动端导航
   */
  closeMobileNav() {
    const navToggle = document.getElementById('navbar-toggle');
    const mobileNav = document.getElementById('navbar-mobile');
    
    if (navToggle && mobileNav) {
      navToggle.classList.remove('active');
      mobileNav.classList.remove('active');
      document.body.style.overflow = '';
    }
  }

  /**
   * 处理登出
   */
  async handleLogout() {
    if (confirm('确定要退出登录吗？')) {
      try {
        this.showLoading('正在退出...');
        await auth.logout();
      } catch (error) {
        console.error('登出失败:', error);
        this.showErrorMessage('登出失败，请重试');
      } finally {
        this.hideLoading();
      }
    }
  }

  /**
   * 处理认证状态变化
   */
  handleAuthChange(event) {
    switch (event.type) {
      case 'auth:login':
        this.updateUserInterface();
        break;
      
      case 'auth:logout':
      case 'auth:unauthorized':
        this.redirectToLogin();
        break;
    }
  }

  /**
   * 处理路由变化
   */
  handleRouterChange(event) {
    const { route } = event.detail;
    
    // 关闭移动端导航
    this.closeMobileNav();
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    console.log(`📍 导航到: ${route.path}`);
  }

  /**
   * 处理路由错误
   */
  handleRouterError(event) {
    const { error, path } = event.detail;
    console.error(`路由错误 (${path}):`, error);
    this.showErrorMessage('页面加载失败，请重试');
  }

  /**
   * 处理窗口大小变化
   */
  handleResize() {
    // 在大屏幕上自动关闭移动端导航
    if (window.innerWidth >= 768) {
      this.closeMobileNav();
    }
  }

  /**
   * 重定向到登录页
   */
  redirectToLogin() {
    window.location.href = '/login.html';
  }

  /**
   * 显示加载指示器
   */
  showLoading(message = '加载中...') {
    if (!this.loadingIndicator) {
      this.loadingIndicator = document.getElementById('loading-indicator');
    }
    
    if (this.loadingIndicator) {
      this.loadingIndicator.querySelector('span:last-child').textContent = message;
      this.loadingIndicator.style.display = 'flex';
    }
  }

  /**
   * 隐藏加载指示器
   */
  hideLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.style.display = 'none';
    }
  }

  /**
   * 显示错误消息
   */
  showErrorMessage(message) {
    // 这里可以实现一个更好的错误提示组件
    alert(message);
  }

  /**
   * 处理初始化错误
   */
  handleInitError(error) {
    this.hideLoading();
    
    // 如果是认证错误，重定向到登录页
    if (error.status === 401) {
      this.redirectToLogin();
      return;
    }
    
    // 显示错误页面
    document.body.innerHTML = `
      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 100vh; padding: 2rem; text-align: center;">
        <h1 style="font-size: 2rem; margin-bottom: 1rem; color: #dc2626;">应用初始化失败</h1>
        <p style="margin-bottom: 2rem; color: #6b7280;">抱歉，应用无法正常启动。请检查网络连接并刷新页面重试。</p>
        <button onclick="window.location.reload()" style="padding: 0.75rem 1.5rem; background: #3b82f6; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">刷新页面</button>
      </div>
    `;
  }
}

// 创建应用实例
const app = new App();

// 等待DOM加载完成后初始化应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => app.init());
} else {
  app.init();
}

// 导出应用实例（用于调试）
window.app = app;

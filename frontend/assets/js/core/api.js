/**
 * API 请求管理模块
 * 统一处理所有API请求，包括认证、错误处理、请求拦截等
 */

class APIClient {
  constructor(baseURL = 'https://api.ios.xxyx.cn') {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
    this.interceptors = {
      request: [],
      response: [],
      error: []
    };

    // 设置默认请求拦截器
    this.setupDefaultInterceptors();
  }

  /**
   * 设置默认拦截器
   */
  setupDefaultInterceptors() {
    // 请求拦截器 - 添加认证token
    this.addRequestInterceptor((config) => {
      const token = this.getToken();
      if (token) {
        config.headers = {
          ...config.headers,
          'Authorization': `Bearer ${token}`
        };
      }
      return config;
    });

    // 响应拦截器 - 处理通用错误
    this.addResponseInterceptor(
      (response) => response,
      (error) => {
        if (error.status === 401) {
          this.handleUnauthorized();
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor) {
    this.interceptors.request.push(interceptor);
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(onFulfilled, onRejected) {
    this.interceptors.response.push({ onFulfilled, onRejected });
  }

  /**
   * 添加错误拦截器
   */
  addErrorInterceptor(interceptor) {
    this.interceptors.error.push(interceptor);
  }

  /**
   * 获取存储的token
   */
  getToken() {
    return localStorage.getItem('token');
  }

  /**
   * 设置token
   */
  setToken(token) {
    localStorage.setItem('token', token);
  }

  /**
   * 清除token
   */
  clearToken() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  /**
   * 处理未授权错误
   */
  handleUnauthorized() {
    this.clearToken();
    // 触发全局事件
    window.dispatchEvent(new CustomEvent('auth:unauthorized'));
    // 重定向到登录页
    if (window.location.pathname !== '/login.html') {
      window.location.href = '/login.html';
    }
  }

  /**
   * 应用请求拦截器
   */
  async applyRequestInterceptors(config) {
    let processedConfig = { ...config };
    
    for (const interceptor of this.interceptors.request) {
      try {
        processedConfig = await interceptor(processedConfig);
      } catch (error) {
        console.error('Request interceptor error:', error);
      }
    }
    
    return processedConfig;
  }

  /**
   * 应用响应拦截器
   */
  async applyResponseInterceptors(response, error = null) {
    let processedResponse = response;
    let processedError = error;

    for (const interceptor of this.interceptors.response) {
      try {
        if (processedError) {
          if (interceptor.onRejected) {
            processedError = await interceptor.onRejected(processedError);
          }
        } else {
          if (interceptor.onFulfilled) {
            processedResponse = await interceptor.onFulfilled(processedResponse);
          }
        }
      } catch (err) {
        console.error('Response interceptor error:', err);
        processedError = err;
      }
    }

    if (processedError) {
      throw processedError;
    }

    return processedResponse;
  }

  /**
   * 构建完整URL
   */
  buildURL(endpoint) {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    return `${this.baseURL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
  }

  /**
   * 处理请求错误
   */
  handleRequestError(error, url, options) {
    const apiError = {
      message: error.message || '请求失败',
      status: error.status || 0,
      url,
      options,
      timestamp: new Date().toISOString()
    };

    // 应用错误拦截器
    for (const interceptor of this.interceptors.error) {
      try {
        interceptor(apiError);
      } catch (err) {
        console.error('Error interceptor failed:', err);
      }
    }

    return apiError;
  }

  /**
   * 通用请求方法
   */
  async request(endpoint, options = {}) {
    const url = this.buildURL(endpoint);

    // 准备请求配置
    let config = {
      method: 'GET',
      headers: options.headers ? { ...options.headers } : { ...this.defaultHeaders },
      ...options,
      url
    };

    try {
      // 应用请求拦截器
      config = await this.applyRequestInterceptors(config);

      // 发送请求
      const response = await fetch(config.url, {
        method: config.method,
        headers: config.headers,
        body: config.body,
        ...config
      });

      // 检查响应状态
      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        error.status = response.status;
        error.response = response;
        throw error;
      }

      // 解析响应
      const contentType = response.headers.get('content-type');
      let data;
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      const result = {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        config
      };

      // 应用响应拦截器
      return await this.applyResponseInterceptors(result);

    } catch (error) {
      const processedError = this.handleRequestError(error, url, config);
      
      try {
        await this.applyResponseInterceptors(null, processedError);
      } catch (interceptorError) {
        throw interceptorError;
      }
      
      throw processedError;
    }
  }

  /**
   * GET 请求
   */
  async get(endpoint, params = {}, options = {}) {
    const url = new URL(this.buildURL(endpoint));
    
    // 添加查询参数
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    return this.request(url.toString(), {
      method: 'GET',
      ...options
    });
  }

  /**
   * POST 请求
   */
  async post(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    });
  }

  /**
   * PUT 请求
   */
  async put(endpoint, data = {}, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      ...options
    });
  }

  /**
   * DELETE 请求
   */
  async delete(endpoint, options = {}) {
    return this.request(endpoint, {
      method: 'DELETE',
      ...options
    });
  }

  /**
   * 文件上传
   */
  async upload(endpoint, formData, options = {}) {
    const uploadOptions = {
      method: 'POST',
      body: formData,
      headers: {}, // 不使用默认headers
      ...options
    };

    // 确保不设置 Content-Type，让浏览器自动设置
    if (uploadOptions.headers && uploadOptions.headers['Content-Type']) {
      delete uploadOptions.headers['Content-Type'];
    }

    return this.request(endpoint, uploadOptions);
  }

  /**
   * 下载文件
   */
  async download(endpoint, filename, options = {}) {
    const response = await this.request(endpoint, {
      ...options,
      headers: {
        ...options.headers,
        'Accept': 'application/octet-stream'
      }
    });

    // 创建下载链接
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return response;
  }
}

// 创建全局API实例
const api = new APIClient();

// 导出API实例和类
export { api as default, APIClient };

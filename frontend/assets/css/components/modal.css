/* 模态框组件样式 */

/* 模态框遮罩层 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  backdrop-filter: blur(4px);
  pointer-events: auto;
}

.modal-backdrop.active {
  opacity: 1;
  visibility: visible;
}

/* 模态框容器 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-base);
  pointer-events: none;
}

.modal.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* 模态框内容 */
.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.95) translateY(20px);
  transition: transform var(--transition-base);
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

.modal.active .modal-content {
  transform: scale(1) translateY(0);
}

/* 模态框尺寸 */
.modal-sm .modal-content {
  max-width: 400px;
}

.modal-md .modal-content {
  max-width: 500px;
}

.modal-lg .modal-content {
  max-width: 700px;
}

.modal-xl .modal-content {
  max-width: 900px;
}

.modal-full .modal-content {
  max-width: none;
  width: calc(100% - var(--space-8));
  height: calc(100% - var(--space-8));
  max-height: none;
}

/* 模态框头部 */
.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
}

.modal-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.modal-close:hover {
  color: var(--text-primary);
  background-color: var(--bg-muted);
}

.modal-close:focus-visible {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

/* 模态框主体 */
.modal-body {
  padding: var(--space-6);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.modal-body:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.modal-body:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

/* 模态框底部 */
.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
}

.modal-footer-start {
  justify-content: flex-start;
}

.modal-footer-center {
  justify-content: center;
}

.modal-footer-between {
  justify-content: space-between;
}

/* 确认对话框样式 */
.modal-confirm .modal-body {
  text-align: center;
  padding: var(--space-8);
}

.modal-confirm-icon {
  font-size: var(--text-5xl);
  margin-bottom: var(--space-4);
}

.modal-confirm-icon.warning {
  color: var(--color-warning);
}

.modal-confirm-icon.error {
  color: var(--color-error);
}

.modal-confirm-icon.success {
  color: var(--color-success);
}

.modal-confirm-icon.info {
  color: var(--color-primary);
}

.modal-confirm-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
}

.modal-confirm-message {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--leading-relaxed);
}

/* 加载状态 */
.modal-loading {
  position: relative;
}

.modal-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.modal-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid var(--border-primary);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: modal-spin 1s linear infinite;
  z-index: 2;
}

@keyframes modal-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 抽屉式模态框 */
.modal-drawer {
  align-items: flex-end;
  justify-content: center;
}

.modal-drawer .modal-content {
  max-width: none;
  width: 100%;
  max-height: 80vh;
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  transform: translateY(100%);
}

.modal-drawer.active .modal-content {
  transform: translateY(0);
}

/* 侧边栏式模态框 */
.modal-sidebar {
  align-items: stretch;
  justify-content: flex-end;
  padding: 0;
}

.modal-sidebar .modal-content {
  max-width: 400px;
  width: 100%;
  max-height: none;
  height: 100%;
  border-radius: 0;
  transform: translateX(100%);
}

.modal-sidebar.active .modal-content {
  transform: translateX(0);
}

.modal-sidebar-left {
  justify-content: flex-start;
}

.modal-sidebar-left .modal-content {
  transform: translateX(-100%);
}

.modal-sidebar-left.active .modal-content {
  transform: translateX(0);
}

/* 全屏模态框 */
.modal-fullscreen {
  padding: 0;
}

.modal-fullscreen .modal-content {
  max-width: none;
  width: 100%;
  height: 100%;
  max-height: none;
  border-radius: 0;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .modal {
    padding: var(--space-2);
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--space-4);
  }

  .modal-body {
    max-height: calc(95vh - 120px);
  }

  .modal-lg .modal-content,
  .modal-xl .modal-content {
    max-width: none;
    width: 100%;
  }

  .modal-sidebar .modal-content {
    max-width: none;
    width: 100%;
  }

  .modal-drawer .modal-content {
    max-height: 90vh;
  }
}

/* 无障碍支持 */
.modal[aria-hidden="true"] {
  display: none;
}

.modal-backdrop[aria-hidden="true"] {
  display: none;
}

/* 防止背景滚动 */
body.modal-open {
  overflow: hidden;
}

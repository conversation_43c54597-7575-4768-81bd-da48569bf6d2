# formatFileSize 函数导入修复总结

## 问题描述

在证书记录渲染时出现JavaScript错误：
```
ReferenceError: formatFileSize is not defined
at certificate.js:963:36
```

## 问题分析

### 1. 函数未导入
- `formatFileSize` 函数在 `frontend/assets/js/core/utils.js` 中已定义
- 但在 `certificate.js` 中没有导入这个函数
- 导致在渲染记录时调用 `formatFileSize(record.file_size)` 时出现未定义错误

### 2. 函数定义位置
在 `frontend/assets/js/core/utils.js` 第40-50行：
```javascript
/**
 * 格式化文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
```

## 修复方案

### 1. 添加函数导入

**修复前**：
```javascript
import { formatDateTime, downloadFile } from '../core/utils.js';
```

**修复后**：
```javascript
import { formatDateTime, downloadFile, formatFileSize } from '../core/utils.js';
```

### 2. 函数使用位置
在 `certificate.js` 第963行的记录渲染中：
```javascript
${record.file_size ? `<div><strong>文件大小:</strong> ${formatFileSize(record.file_size)}</div>` : ''}
```

## 函数功能说明

### formatFileSize 函数特性：
- **输入**: 字节数 (number)
- **输出**: 格式化的文件大小字符串
- **支持单位**: Bytes, KB, MB, GB, TB, PB, EB, ZB, YB
- **精度控制**: 默认保留2位小数

### 使用示例：
```javascript
formatFileSize(0)        // "0 Bytes"
formatFileSize(1024)     // "1 KB"
formatFileSize(1048576)  // "1 MB"
formatFileSize(1073741824) // "1 GB"
```

## 其他页面的使用情况

### 已正确导入的页面：
1. **upload.js**: `import { formatFileSize, formatDateTime, generateQRCodeURL } from '../core/utils.js';`
2. **resign.js**: 通过全局函数使用（旧版本兼容）

### 函数在项目中的分布：
- **核心定义**: `frontend/assets/js/core/utils.js`
- **旧版本**: 各个页面都有独立的 `formatFileSize` 函数定义
- **新版本**: 统一使用 `utils.js` 中的导出函数

## 验证结果

### 修复后的效果：
- ✅ 证书记录列表正常显示
- ✅ 文件大小正确格式化显示
- ✅ 不再出现 `formatFileSize is not defined` 错误
- ✅ 其他功能不受影响

### 显示示例：
```
证书记录卡片：
┌─────────────────────────────────┐
│ 📋 MyApp Certificate            │
│ CER转P12                        │
│                           [P12] │
├─────────────────────────────────┤
│ 证书类型: 发布证书              │
│ 创建时间: 2024-01-01 10:00      │
│ 状态: [已完成]                  │
│ 文件大小: 2.5 KB               │ ← 正确显示
└─────────────────────────────────┘
```

## 最佳实践

### 1. 统一导入管理
- 所有工具函数都从 `core/utils.js` 导入
- 避免在各个页面重复定义相同功能的函数
- 保持代码的一致性和可维护性

### 2. 函数依赖检查
- 在使用函数前确保已正确导入
- 使用IDE的自动导入功能
- 定期检查未使用的导入

### 3. 错误预防
- 使用TypeScript或JSDoc进行类型检查
- 设置ESLint规则检查未定义变量
- 在开发环境中启用严格模式

## 总结

通过在 `certificate.js` 中正确导入 `formatFileSize` 函数，修复了证书记录渲染时的未定义错误。这个修复：

1. **解决了即时问题**: 消除了JavaScript运行时错误
2. **保持了一致性**: 使用统一的工具函数
3. **提高了可维护性**: 避免重复代码
4. **改善了用户体验**: 文件大小正确显示

现在证书管理功能的记录列表应该能够完全正常工作，包括正确显示文件大小信息。

# 存储文件访问问题修复指南

## 问题描述

当前访问 `https://api.ios.xxyx.cn/storage/uploads/icons/resign_68a8395e569d80105500f6c2.png` 时返回：
```json
{"success":false,"error":"接口不存在"}
```

但文件实际存在于 `/data/storage/uploads/icons/resign_68a8395e569d80105500f6c2.png`

## 问题原因

Caddy配置中的 `try_files` 指令拦截了 `/storage/*` 路径，导致这些请求被转发到 `index.php` 处理，而不是直接提供静态文件服务。

## 修复方案

### 方案1：自动修复（推荐）

运行修复脚本：

```bash
# 1. 给脚本添加执行权限
chmod +x scripts/fix-storage-access-issue.sh
chmod +x scripts/test-storage-access.sh

# 2. 运行修复脚本（需要root权限）
sudo ./scripts/fix-storage-access-issue.sh

# 3. 测试修复效果
./scripts/test-storage-access.sh
```

### 方案2：手动修复

1. **备份当前配置**：
   ```bash
   sudo cp /etc/caddy/sites/api.ios.xxyx.cn.conf /etc/caddy/sites/api.ios.xxyx.cn.conf.backup
   ```

2. **更新Caddy配置**：
   编辑 `/etc/caddy/sites/api.ios.xxyx.cn.conf`，将配置改为：

   ```caddy
   api.ios.xxyx.cn {
       # 请求体大小限制
       request_body {
           max_size 2GB
       }

       # 正确的根目录 - 指向public目录
       root * /data/www/api.ios.xxyx.cn/public

       # 存储文件访问 - 最高优先级，使用handle指令避免被try_files拦截
       handle /storage/* {
           root * /data
           file_server {
               hide .env .git .htaccess .sql .bak
           }
           header Cache-Control "public, max-age=86400"
           header Access-Control-Allow-Origin "*"
       }
       
       # API路由处理 - 只对非存储路径生效
       handle {
           try_files {path} /index.php
           php_fastcgi unix//run/php/php8.3-fpm.sock
       }
       
       # CORS头设置
       header {
           Access-Control-Allow-Origin "*"
           Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
           Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
           -Server
       }
       
       # 处理OPTIONS预检请求
       @options method OPTIONS
       respond @options 200
       
       # 启用压缩
       encode gzip zstd
       
       # 日志记录
       log {
           output file /data/logs/access/api.ios.xxyx.cn.log {
               roll_size 100mb
               roll_keep 10
           }
           format json
       }
   }
   ```

3. **修复权限**：
   ```bash
   sudo chown -R caddy:caddy /data/storage
   sudo chmod -R 755 /data/storage
   ```

4. **验证并重载配置**：
   ```bash
   sudo caddy validate --config /etc/caddy/Caddyfile
   sudo systemctl reload caddy
   ```

## 关键改动说明

1. **使用 `handle` 指令**：替代 `route` 指令，确保 `/storage/*` 路径优先处理
2. **分离处理逻辑**：存储文件和API请求分别处理，避免冲突
3. **统一权限**：所有存储文件归属于 `caddy:caddy`，权限为 `755`

## 验证修复效果

运行测试脚本：
```bash
./scripts/test-storage-access.sh
```

或手动测试：
```bash
curl -I https://api.ios.xxyx.cn/storage/uploads/icons/resign_68a8395e569d80105500f6c2.png
```

期望返回：
```
HTTP/2 200 
content-type: image/png
content-length: 31378
cache-control: public, max-age=86400
```

## 故障排查

如果修复后仍有问题，请检查：

1. **Caddy服务状态**：
   ```bash
   sudo systemctl status caddy
   ```

2. **配置语法**：
   ```bash
   sudo caddy validate --config /etc/caddy/Caddyfile
   ```

3. **查看日志**：
   ```bash
   sudo journalctl -u caddy -f
   tail -f /data/logs/access/api.ios.xxyx.cn.log
   ```

4. **DNS解析**：
   ```bash
   nslookup api.ios.xxyx.cn
   ```

5. **防火墙设置**：
   ```bash
   sudo ufw status
   ```

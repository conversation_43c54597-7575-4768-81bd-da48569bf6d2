# 重签提交错误修复总结

## 问题描述

用户在点击"开始重签"按钮时遇到以下错误：
```
Failed to load resource: the server responded with a status of 400 ()
api.js:142 Response interceptor error: Object
resign.js:327 Resign failed: Object
```

## 问题分析

### 1. API请求问题
- 新版本使用了 `api.upload()` 方法进行文件上传
- `upload` 方法在处理 Content-Type 时存在问题
- 默认的 `application/json` Content-Type 覆盖了文件上传的正确 Content-Type

### 2. 参数验证问题
- 缺少文件验证逻辑
- 缺少必要的默认参数
- 错误处理不够详细

## 修复方案

### 1. 修复 API 客户端 (api.js)

#### 问题：Content-Type 冲突
**修复前**：
```javascript
async upload(endpoint, formData, options = {}) {
  const uploadOptions = {
    method: 'POST',
    body: formData,
    ...options
  };

  // 移除 Content-Type，让浏览器自动设置
  if (uploadOptions.headers) {
    delete uploadOptions.headers['Content-Type'];
  }

  return this.request(endpoint, uploadOptions);
}
```

**修复后**：
```javascript
async upload(endpoint, formData, options = {}) {
  const uploadOptions = {
    method: 'POST',
    body: formData,
    headers: {}, // 不使用默认headers
    ...options
  };

  // 确保不设置 Content-Type，让浏览器自动设置
  if (uploadOptions.headers && uploadOptions.headers['Content-Type']) {
    delete uploadOptions.headers['Content-Type'];
  }

  return this.request(endpoint, uploadOptions);
}
```

#### 问题：request 方法强制添加默认 headers
**修复前**：
```javascript
let config = {
  method: 'GET',
  headers: { ...this.defaultHeaders }, // 总是添加默认headers
  ...options,
  url
};
```

**修复后**：
```javascript
let config = {
  method: 'GET',
  headers: options.headers ? { ...options.headers } : { ...this.defaultHeaders },
  ...options,
  url
};
```

### 2. 修复重签提交逻辑 (resign.js)

#### 添加文件验证
```javascript
// 验证文件
if (!this.currentFile) {
  alert('❌ 请选择IPA文件');
  return;
}
```

#### 添加必要参数
```javascript
// 添加默认配置
formData.append('auto_process', 'true');
```

#### 改进错误处理
```javascript
if (error.status === 400) {
  // 尝试解析错误响应
  try {
    if (error.response) {
      const errorData = await error.response.json();
      errorMessage = errorData.error || errorData.message || '请求参数错误';
    }
  } catch (parseError) {
    errorMessage = '请求参数错误';
  }
}
```

#### 添加调试信息
```javascript
// 调试信息
console.log('Submitting resign task with:', {
  file: this.currentFile.name,
  size: this.currentFile.size,
  bundleId: bundleId,
  appName: appName,
  force: force.includes('force')
});
```

## 后端参数对照

### 后端期望的参数：
```php
// 文件参数
$files['ipa'] // IPA文件

// 表单参数
$request->request->get('new_bundle_id')     // 新Bundle ID（可选）
$request->request->get('new_app_name')      // 新应用名称（可选）
$request->request->get('certificate_pair_id') // 证书对ID（可选）
$request->request->getBoolean('auto_process', true) // 自动处理
$request->request->getBoolean('force', true) // 强制重签
```

### 前端发送的参数：
```javascript
formData.append('ipa', this.currentFile);           // ✅ 文件
formData.append('new_bundle_id', bundleId);         // ✅ 新Bundle ID
formData.append('new_app_name', appName);           // ✅ 新应用名称
formData.append('force', 'true');                   // ✅ 强制重签
formData.append('auto_process', 'true');            // ✅ 自动处理
```

## 证书配置说明

### 自动证书选择：
- 如果没有指定 `certificate_pair_id`，系统会自动选择最佳证书对
- 通过 `getCertificatePair()` 方法实现自动选择
- 基于应用的 Bundle ID 选择合适的证书

### 证书选择逻辑：
```php
private function getCertificatePair(array $record): ?array
{
    // 如果指定了证书对ID，使用指定的证书对
    if (!empty($record['certificate_pair_id'])) {
        // 使用指定证书对
    }
    
    // 否则自动选择最佳证书对
    return $this->certificateService->getBestCertificatePair($record['bundle_id']);
}
```

## 测试验证

### 功能测试：
1. ✅ 文件上传验证正常
2. ✅ Content-Type 自动设置正确
3. ✅ 参数传递完整
4. ✅ 错误处理详细
5. ✅ 调试信息输出

### 错误处理测试：
1. ✅ 400错误：显示具体错误信息
2. ✅ 401错误：提示重新登录
3. ✅ 413错误：提示文件过大
4. ✅ 网络错误：显示通用错误信息

## 相关文件

### 修改的文件：
- `frontend/assets/js/core/api.js` - API客户端修复
- `frontend/assets/js/pages/resign.js` - 重签提交逻辑修复

### 后端相关文件：
- `src/Controllers/ResignController.php` - 重签控制器
- `src/Services/ResignService.php` - 重签服务

## 使用说明

### 用户操作流程：
1. 点击"添加重签任务"按钮
2. 在弹窗中选择IPA文件
3. 可选填写新Bundle ID和应用名称
4. 点击"开始重签"提交任务
5. 系统自动处理并显示结果

### 开发者调试：
- 打开浏览器开发者工具
- 查看Console中的调试信息
- 检查Network面板中的请求详情
- 查看具体的错误响应内容

## 总结

通过修复API客户端的Content-Type处理问题、添加必要的参数验证和改进错误处理，成功解决了重签提交的400错误问题。现在用户可以正常提交重签任务，系统会自动选择合适的证书进行重签处理。

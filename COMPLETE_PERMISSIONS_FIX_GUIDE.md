# 完整权限问题修复指南

## 问题概述

在XIOS系统中出现了多个权限相关的错误：

1. **图标提取权限错误**：`mkdir(): Permission denied` - 图标目录创建失败
2. **IPA文件复制权限错误**：`copy(): Failed to open stream: Permission denied` - IPA文件无法复制到存储目录
3. **Plist文件生成权限错误**：`file_put_contents(): Failed to open stream: Permission denied` - plist文件无法创建

## 根本原因

1. **存储目录结构不完整**：缺少必要的存储目录
2. **权限设置不正确**：PHP进程无法写入存储目录
3. **用户组配置问题**：Web服务器用户与PHP进程用户权限不匹配

## 完整的目录结构

系统需要以下目录结构：

```
/data/storage/                    # 主存储目录
├── uploads/                      # 上传文件目录
│   ├── icons/                   # 应用图标存储
│   └── ipa/                     # 上传的IPA文件
├── resigned_ipas/               # 重签后的IPA文件
├── plists/                      # iOS安装plist文件
├── qrcodes/                     # 二维码图片
└── temp/                        # 临时文件

/tmp/ipa_uploads/                # 临时上传目录
```

## 🚀 一键修复解决方案

### 方案1：完整权限修复（推荐）

```bash
# 给脚本添加执行权限
chmod +x scripts/fix-storage-permissions.sh

# 运行完整修复脚本
sudo ./scripts/fix-storage-permissions.sh
```

### 方案2：快速修复plist权限

如果只是plist权限问题：

```bash
# 给脚本添加执行权限
chmod +x scripts/quick-fix-plist-permissions.sh

# 运行快速修复脚本
sudo ./scripts/quick-fix-plist-permissions.sh
```

### 方案3：手动修复

如果脚本无法运行，可以手动执行：

```bash
# 1. 创建所有必要目录
sudo mkdir -p /data/storage/uploads/icons
sudo mkdir -p /data/storage/uploads/ipa
sudo mkdir -p /data/storage/resigned_ipas
sudo mkdir -p /data/storage/plists
sudo mkdir -p /data/storage/qrcodes
sudo mkdir -p /data/storage/temp
sudo mkdir -p /tmp/ipa_uploads

# 2. 设置正确的所有者
sudo chown -R caddy:caddy /data/storage
sudo chown -R caddy:caddy /tmp/ipa_uploads

# 3. 设置正确的权限
sudo chmod -R 755 /data/storage
sudo chmod -R 775 /data/storage/uploads
sudo chmod -R 775 /data/storage/resigned_ipas
sudo chmod -R 775 /data/storage/plists
sudo chmod -R 775 /data/storage/qrcodes
sudo chmod -R 775 /data/storage/temp
sudo chmod -R 775 /tmp/ipa_uploads

# 4. 重启服务
sudo systemctl restart php8.3-fpm
sudo systemctl restart caddy
```

## 验证修复效果

### 1. 运行权限检查

```bash
php scripts/check-permissions.php
```

### 2. 测试各个功能

```bash
# 测试图标目录
sudo -u caddy touch /data/storage/uploads/icons/test.png
ls -la /data/storage/uploads/icons/test.png
sudo rm /data/storage/uploads/icons/test.png

# 测试IPA目录
sudo -u caddy touch /data/storage/uploads/ipa/test.ipa
ls -la /data/storage/uploads/ipa/test.ipa
sudo rm /data/storage/uploads/ipa/test.ipa

# 测试plist目录
sudo -u caddy touch /data/storage/plists/test.plist
ls -la /data/storage/plists/test.plist
sudo rm /data/storage/plists/test.plist
```

### 3. 功能测试

1. **重签功能测试**：
   - 上传一个IPA文件
   - 点击开始重签
   - 检查是否能正常生成重签文件和安装链接

2. **上传功能测试**：
   - 上传一个IPA文件到TestFlight
   - 检查文件是否正常保存

3. **图标显示测试**：
   - 检查应用图标是否正常显示
   - 访问图标URL确认可以正常加载

## 权限配置详解

### 目录权限说明

- **755权限**：所有者可读写执行，组和其他用户可读执行
- **775权限**：所有者和组可读写执行，其他用户可读执行
- **644权限**：所有者可读写，组和其他用户只读

### 用户和组配置

- **caddy:caddy**：Caddy Web服务器的用户和组
- **PHP-FPM进程**：通常也运行在caddy用户下
- **文件上传**：通过PHP处理，需要caddy用户有写权限

## 环境变量配置

确保 `/data/www/api.ios.xxyx.cn/.env` 文件包含正确的存储路径：

```env
# 存储路径配置
IPA_STORAGE_PATH=/data/storage/uploads/ipa
ICON_STORAGE_PATH=/data/storage/uploads/icons
APP_STORAGE_PATH=/data/storage
```

## 常见问题排查

### 1. 权限仍然被拒绝

**可能原因**：SELinux启用

**解决方案**：
```bash
# 检查SELinux状态
sestatus

# 如果启用，设置正确的上下文
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P httpd_unified 1
sudo restorecon -R /data/storage
```

### 2. 磁盘空间不足

**检查空间**：
```bash
df -h /data
```

**清理空间**：
```bash
# 清理临时文件
sudo find /tmp -name "ipa_*" -mtime +1 -delete
sudo find /data/storage/temp -mtime +1 -delete
```

### 3. PHP-FPM用户配置

**检查配置**：
```bash
grep -E "^(user|group)" /etc/php/8.3/fpm/pool.d/www.conf
```

**应该显示**：
```
user = caddy
group = caddy
```

### 4. Web服务器配置

确保Caddy配置支持存储目录访问：

```caddy
ios.xxyx.cn {
    # 存储文件访问
    handle_path /storage/* {
        root * /data
        file_server
    }
    
    # 其他配置...
}
```

## 监控和维护

### 1. 定期权限检查

添加到crontab：
```bash
# 每天凌晨2点检查权限
0 2 * * * php /data/www/api.ios.xxyx.cn/scripts/check-permissions.php
```

### 2. 日志监控

```bash
# 实时查看PHP错误日志
tail -f /data/logs/php/error.log

# 查看Web服务器访问日志
tail -f /data/logs/access/ios.xxyx.cn.log
```

### 3. 磁盘空间监控

```bash
# 检查磁盘使用率
df -h /data | awk 'NR==2 {print $5}' | sed 's/%//'
```

## 部署时自动修复

已更新 `deploy.sh` 脚本，在部署时自动创建目录和设置权限：

```bash
# 修复存储目录权限
mkdir -p /data/storage/uploads/icons
mkdir -p /data/storage/uploads/ipa
mkdir -p /data/storage/resigned_ipas
mkdir -p /data/storage/plists
mkdir -p /data/storage/qrcodes
mkdir -p /data/storage/temp

# 设置正确的权限和所有者
chown -R caddy:caddy /data/storage
chmod -R 755 /data/storage
chmod -R 775 /data/storage/uploads
chmod -R 775 /data/storage/temp
```

## 总结

通过以上修复步骤，可以彻底解决XIOS系统中的所有权限问题：

1. ✅ **图标提取功能**：正常工作，图标可以正常显示
2. ✅ **文件上传功能**：IPA文件可以正常保存
3. ✅ **重签功能**：重签文件可以正常生成
4. ✅ **分发功能**：plist文件和安装链接正常工作
5. ✅ **系统稳定性**：不再出现权限相关的PHP错误

**建议立即运行完整修复脚本来解决所有权限问题！**

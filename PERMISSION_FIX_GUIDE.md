# 存储目录权限问题修复指南

## 问题描述

当前遇到多个权限相关错误：

1. **temp目录权限错误**：
   ```
   copy(/data/storage/temp/original_xxx.ipa): Failed to open stream: Permission denied
   ```

2. **图标目录权限错误**：
   ```
   copy(/data/storage/uploads/icons/resign_xxx.png): Failed to open stream: Permission denied
   ```

## 根本原因

- PHP-FPM进程以特定用户运行（通常是`www-data`）
- 存储目录所有者是`caddy`用户
- 目录权限不足，PHP进程无法写入文件

## 快速修复方案

### 方案1：一键快速修复（推荐）

```bash
# 给脚本添加执行权限
chmod +x scripts/quick-fix-permissions.sh

# 运行快速修复
sudo ./scripts/quick-fix-permissions.sh
```

### 方案2：手动修复

```bash
# 1. 检查PHP-FPM用户
ps aux | grep php-fpm | grep -v root | head -1

# 2. 创建必要目录
sudo mkdir -p /data/storage/{temp,resigned_ipas,uploads/{ipa,icons},certificates,plists,qrcodes}

# 3. 设置所有者（假设PHP-FPM用户是www-data）
sudo chown -R www-data:www-data /data/storage

# 4. 设置权限
sudo chmod -R 755 /data/storage
sudo chmod 777 /data/storage/temp
sudo chmod 777 /data/storage/uploads/icons
sudo chmod 777 /data/storage/uploads/ipa
sudo chmod 777 /data/storage/resigned_ipas

# 5. 重启PHP-FPM
sudo systemctl restart php8.3-fpm
```

### 方案3：完整诊断和修复

```bash
# 运行诊断脚本
chmod +x scripts/diagnose-temp-permissions.sh
./scripts/diagnose-temp-permissions.sh

# 运行完整修复脚本
chmod +x scripts/fix-temp-permissions.sh
sudo ./scripts/fix-temp-permissions.sh
```

## 验证修复效果

### 1. 检查目录权限
```bash
ls -la /data/storage/
ls -la /data/storage/uploads/
```

### 2. 测试文件创建
```bash
# 测试temp目录
sudo -u www-data touch /data/storage/temp/test.txt
ls -la /data/storage/temp/test.txt
sudo rm /data/storage/temp/test.txt

# 测试图标目录
sudo -u www-data touch /data/storage/uploads/icons/test.png
ls -la /data/storage/uploads/icons/test.png
sudo rm /data/storage/uploads/icons/test.png
```

### 3. 测试API功能
```bash
# 上传一个IPA文件测试
curl -X POST https://api.ios.xxyx.cn/api/resign/submit \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.ipa"
```

## 权限说明

### 目录权限设置

- **755权限** (`rwxr-xr-x`)：所有者可读写执行，组和其他用户可读执行
- **777权限** (`rwxrwxrwx`)：所有用户可读写执行（用于需要写入的目录）

### 关键目录用途

- `/data/storage/temp/` - 临时文件存储（IPA处理过程中）
- `/data/storage/uploads/icons/` - 应用图标存储
- `/data/storage/uploads/ipa/` - 上传的IPA文件存储
- `/data/storage/resigned_ipas/` - 重签后的IPA文件存储

## 故障排查

### 如果修复后仍有问题

1. **检查SELinux**：
   ```bash
   sestatus
   # 如果启用，可能需要设置SELinux上下文
   ```

2. **检查PHP错误日志**：
   ```bash
   tail -f /var/log/php8.3-fpm.log
   ```

3. **检查系统日志**：
   ```bash
   journalctl -u php8.3-fpm -f
   ```

4. **验证PHP-FPM配置**：
   ```bash
   grep -E "^(user|group)" /etc/php/8.3/fpm/pool.d/www.conf
   ```

### 常见问题

1. **权限修复后立即失效**：
   - 可能有其他进程在重置权限
   - 检查是否有定时任务或监控脚本

2. **特定文件仍无法创建**：
   - 检查父目录权限
   - 确认磁盘空间充足

3. **PHP-FPM用户检测错误**：
   - 手动指定用户：`sudo chown -R www-data:www-data /data/storage`

## 安全注意事项

- 777权限相对宽松，仅用于必要的写入目录
- 生产环境建议使用更精确的权限控制
- 定期检查和清理临时文件

## 相关文件

- `scripts/quick-fix-permissions.sh` - 快速修复脚本
- `scripts/fix-temp-permissions.sh` - 完整修复脚本
- `scripts/diagnose-temp-permissions.sh` - 诊断脚本

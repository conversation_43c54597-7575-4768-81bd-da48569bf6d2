# 图标路径一致性修复

## 问题描述

发现了一个严重的路径不一致问题：

### 修复前的问题

1. **IpaParser.php** (图标生成)：
   - 生成URL：`/storage/uploads/icons/resign_xxx.png`
   - 实际存储：`/data/storage/uploads/icons/resign_xxx.png`

2. **ResignService.php** (图标删除)：
   - 期望URL：`/storage/img/icons/resign_xxx.png`
   - 查找路径：`/storage/img/icons/resign_xxx.png`

### 问题影响

- 图标文件正常生成和访问
- 但删除重签记录时，图标文件无法被正确删除
- 导致存储空间浪费

## 修复方案

### 修复内容

修改了 `ResignService.php` 中的 `getIconPathFromUrl()` 方法：

```php
/**
 * 从图标URL获取文件路径
 */
private function getIconPathFromUrl(string $iconUrl): ?string
{
    // 图标URL格式: /storage/uploads/icons/resign_recordId.png
    if (strpos($iconUrl, '/storage/uploads/icons/') === 0) {
        $filename = basename($iconUrl);
        // 使用配置中的图标存储路径
        $config = AppConfig::getInstance();
        $iconDir = $config->get('icon_storage_path') ?? '/data/storage/uploads/icons';
        return $iconDir . '/' . $filename;
    }
    return null;
}
```

### 修复后的统一路径

现在两个文件使用相同的路径格式：

1. **图标生成** (IpaParser.php)：
   - URL：`/storage/uploads/icons/resign_xxx.png`
   - 存储：`/data/storage/uploads/icons/resign_xxx.png`

2. **图标删除** (ResignService.php)：
   - 识别URL：`/storage/uploads/icons/resign_xxx.png`
   - 删除路径：`/data/storage/uploads/icons/resign_xxx.png`

## 验证修复效果

### 1. 测试图标生成
```bash
# 上传一个IPA文件，检查图标是否正常生成
curl -X POST https://api.ios.xxyx.cn/api/upload/ipa \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test.ipa"
```

### 2. 测试图标访问
```bash
# 访问生成的图标URL
curl -I https://api.ios.xxyx.cn/storage/uploads/icons/resign_xxx.png
```

### 3. 测试记录删除
```bash
# 删除重签记录，检查图标文件是否被正确删除
curl -X DELETE https://api.ios.xxyx.cn/api/resign/records/xxx \
  -H "Authorization: Bearer YOUR_TOKEN"

# 检查图标文件是否已删除
ls -la /data/storage/uploads/icons/resign_xxx.png
```

## 相关配置

确保环境变量配置正确：

```env
# .env 文件
ICON_STORAGE_PATH=/data/storage/uploads/icons
```

## 注意事项

1. **历史数据**：如果系统中存在使用旧路径格式的历史数据，这些图标文件可能无法被正确删除
2. **权限检查**：确保Web服务器用户对存储目录有读写权限
3. **Caddy配置**：确保Caddy配置正确处理 `/storage/uploads/icons/` 路径

## 相关文件

- `src/Utils/IpaParser.php` - 图标提取和URL生成
- `src/Services/ResignService.php` - 图标删除逻辑
- `scripts/fix-storage-access-issue.sh` - 存储访问修复脚本

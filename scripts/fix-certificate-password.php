<?php

/**
 * 修复证书密码问题 - 手动更新证书的密码字段
 * 
 * 使用方法：
 * 1. 修改 $certificateId 为需要更新的证书ID
 * 2. 修改 $actualPassword 为实际的证书密码
 * 3. 运行脚本：php scripts/fix-certificate-password.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\Certificate;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔧 修复证书密码问题\n";
echo "==================\n\n";

// 配置需要修复的证书
$certificateId = '68a291761f10c8f7250a6c22'; // 替换为实际的证书ID
$actualPassword = '123456'; // 替换为实际的证书密码

echo "⚠️  即将更新证书密码:\n";
echo "证书ID: $certificateId\n";
echo "密码: $actualPassword\n\n";

try {
    $certificateModel = new Certificate();
    
    // 获取反射对象
    $reflection = new ReflectionClass($certificateModel);
    $collectionProperty = $reflection->getProperty('collection');
    $collectionProperty->setAccessible(true);
    $collection = $collectionProperty->getValue($certificateModel);
    
    // 首先查找证书记录
    $cert = $collection->findOne(['_id' => new MongoDB\BSON\ObjectId($certificateId)]);
    
    if (!$cert) {
        echo "❌ 未找到指定的证书记录\n";
        exit(1);
    }
    
    echo "📋 找到证书记录:\n";
    echo "名称: " . $cert['name'] . "\n";
    echo "文件路径: " . $cert['file_path'] . "\n";
    echo "Team ID: " . ($cert['team_id'] ?? '未设置') . "\n";
    echo "Team名称: " . ($cert['team_name'] ?? '未设置') . "\n\n";
    
    // 验证密码是否正确
    if (file_exists($cert['file_path'])) {
        echo "🔍 验证密码正确性...\n";
        
        $testCommand = 'openssl pkcs12 -info -in ' . escapeshellarg($cert['file_path']) . 
                      ' -passin pass:' . escapeshellarg($actualPassword) . ' -noout -legacy 2>&1';
        
        $testOutput = shell_exec($testCommand);
        $testSuccess = (strpos($testOutput, 'MAC verified OK') !== false || 
                       strpos($testOutput, 'Certificate') !== false);
        
        if ($testSuccess) {
            echo "✅ 密码验证成功\n\n";
        } else {
            echo "❌ 密码验证失败\n";
            echo "错误输出: " . trim($testOutput) . "\n";
            echo "请检查密码是否正确\n";
            exit(1);
        }
    } else {
        echo "⚠️  证书文件不存在，跳过密码验证\n\n";
    }
    
    // 更新数据库记录
    echo "💾 更新数据库记录...\n";
    
    $updateResult = $collection->updateOne(
        ['_id' => new MongoDB\BSON\ObjectId($certificateId)],
        [
            '$set' => [
                'password' => $actualPassword,
                'password_hash' => password_hash($actualPassword, PASSWORD_DEFAULT)
            ]
        ]
    );
    
    if ($updateResult->getModifiedCount() > 0) {
        echo "✅ 密码更新成功\n";
        echo "更新了 " . $updateResult->getModifiedCount() . " 个记录\n\n";
        
        echo "🎉 修复完成！\n";
        echo "现在可以测试重签功能了：\n";
        echo "1. 访问: https://ios.xxyx.cn/resign.html\n";
        echo "2. 上传IPA文件进行重签\n";
        echo "3. 检查任务状态是否变为 'completed'\n";
        
    } else {
        echo "❌ 密码更新失败\n";
        echo "可能的原因:\n";
        echo "1. 证书ID不正确\n";
        echo "2. 数据库连接问题\n";
        echo "3. 记录已经包含相同的密码\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 修复过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n💡 提示:\n";
echo "- 如果需要修复其他证书，请修改脚本顶部的配置\n";
echo "- 常见密码包括: 123456, password, test, 空密码等\n";
echo "- 修复后建议重新上传证书时提供正确密码\n";

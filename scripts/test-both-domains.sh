#!/bin/bash

echo "🔍 测试两个域名的存储文件访问..."
echo "================================"

# 创建测试文件
TEST_ICON="/data/storage/uploads/icons/domain_test.png"
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
chmod 644 "$TEST_ICON"
chown caddy:caddy "$TEST_ICON" 2>/dev/null || true

echo "✅ 测试文件已创建: $TEST_ICON"

# 测试两个域名
DOMAINS=(
    "https://ios.xxyx.cn"
    "https://api.ios.xxyx.cn"
)

echo ""
echo "🌐 测试域名访问..."

for DOMAIN in "${DOMAINS[@]}"; do
    TEST_URL="$DOMAIN/storage/uploads/icons/domain_test.png"
    
    echo ""
    echo "========================================="
    echo "测试域名: $DOMAIN"
    echo "测试URL: $TEST_URL"
    echo "========================================="
    
    # 获取完整响应头
    RESPONSE=$(curl -s -I --max-time 10 --insecure "$TEST_URL")
    
    if [ $? -eq 0 ]; then
        echo "📡 响应头:"
        echo "$RESPONSE"
        
        # 提取关键信息
        HTTP_CODE=$(echo "$RESPONSE" | head -1 | cut -d' ' -f2)
        CONTENT_TYPE=$(echo "$RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        CONTENT_LENGTH=$(echo "$RESPONSE" | grep -i "content-length" | cut -d' ' -f2- | tr -d '\r')
        
        echo ""
        echo "📊 关键信息:"
        echo "HTTP状态码: $HTTP_CODE"
        echo "Content-Type: $CONTENT_TYPE"
        echo "Content-Length: $CONTENT_LENGTH"
        
        # 判断结果
        if [ "$HTTP_CODE" = "200" ]; then
            if echo "$CONTENT_TYPE" | grep -q "image"; then
                echo "✅ 成功 - 正确返回图片"
            elif echo "$CONTENT_TYPE" | grep -q "html"; then
                echo "❌ 失败 - 返回HTML页面而不是图片"
            else
                echo "⚠️  成功但Content-Type未知: $CONTENT_TYPE"
            fi
        else
            echo "❌ 失败 - HTTP状态码: $HTTP_CODE"
        fi
    else
        echo "❌ 连接失败 - 可能是网络问题或域名解析问题"
    fi
done

# 清理测试文件
rm -f "$TEST_ICON"

echo ""
echo ""
echo "📋 建议:"
echo "1. 如果API域名(api.ios.xxyx.cn)能正确返回图片，使用API域名"
echo "2. 如果前端域名(ios.xxyx.cn)能正确返回图片，继续使用前端域名"
echo "3. 如果两个都返回HTML，说明Caddy配置需要调整"
echo "4. 如果连接失败，检查DNS解析和防火墙设置"

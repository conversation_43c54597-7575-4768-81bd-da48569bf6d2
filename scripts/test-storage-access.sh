#!/bin/bash

# 测试存储文件访问
# 验证 https://api.ios.xxyx.cn/storage/* 路径是否正常工作

echo "🧪 测试API存储文件访问..."
echo "================================"

# 检查存储目录
echo "📁 检查存储目录..."
if [ -d "/data/storage/uploads/icons" ]; then
    echo "✅ 图标存储目录存在"
    
    # 显示目录权限
    echo "📋 目录权限："
    ls -ld /data/storage/uploads/icons
    
    # 显示文件列表
    echo ""
    echo "📋 图标文件列表："
    ls -la /data/storage/uploads/icons/ | head -5
    
    # 统计文件数量
    FILE_COUNT=$(ls -1 /data/storage/uploads/icons/*.png 2>/dev/null | wc -l)
    echo "📊 图标文件数量: $FILE_COUNT"
else
    echo "❌ 图标存储目录不存在"
    exit 1
fi

echo ""
echo "🌐 测试Web访问..."

# 测试多个图标文件
TESTED=0
SUCCESS=0

for ICON_FILE in /data/storage/uploads/icons/*.png; do
    if [ -f "$ICON_FILE" ] && [ $TESTED -lt 3 ]; then
        ICON_NAME=$(basename "$ICON_FILE")
        TEST_URL="https://api.ios.xxyx.cn/storage/uploads/icons/$ICON_NAME"
        
        echo ""
        echo "🔗 测试: $ICON_NAME"
        echo "URL: $TEST_URL"
        
        # 使用curl测试，获取详细信息
        RESPONSE=$(curl -s -I "$TEST_URL")
        HTTP_CODE=$(echo "$RESPONSE" | head -1 | cut -d' ' -f2)
        CONTENT_TYPE=$(echo "$RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        CONTENT_LENGTH=$(echo "$RESPONSE" | grep -i "content-length" | cut -d' ' -f2 | tr -d '\r')
        
        echo "HTTP状态码: $HTTP_CODE"
        echo "内容类型: $CONTENT_TYPE"
        echo "文件大小: $CONTENT_LENGTH bytes"
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ 访问成功"
            SUCCESS=$((SUCCESS + 1))
        else
            echo "❌ 访问失败"
            
            # 显示错误响应内容
            ERROR_CONTENT=$(curl -s "$TEST_URL")
            echo "错误内容: $ERROR_CONTENT"
        fi
        
        TESTED=$((TESTED + 1))
    fi
done

echo ""
echo "📊 测试结果："
echo "测试文件数: $TESTED"
echo "成功访问: $SUCCESS"
echo "失败数量: $((TESTED - SUCCESS))"

if [ $SUCCESS -eq $TESTED ] && [ $TESTED -gt 0 ]; then
    echo "🎉 所有测试通过！存储文件访问正常"
    exit 0
elif [ $SUCCESS -gt 0 ]; then
    echo "⚠️  部分测试通过，可能存在配置问题"
    exit 1
else
    echo "❌ 所有测试失败，存储文件访问异常"
    
    echo ""
    echo "🔍 故障排查建议："
    echo "1. 检查Caddy服务状态: systemctl status caddy"
    echo "2. 检查Caddy配置: caddy validate --config /etc/caddy/Caddyfile"
    echo "3. 查看Caddy日志: journalctl -u caddy -f"
    echo "4. 检查DNS解析: nslookup api.ios.xxyx.cn"
    echo "5. 检查防火墙: ufw status"
    
    exit 1
fi

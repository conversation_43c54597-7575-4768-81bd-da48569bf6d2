#!/bin/bash

echo "🚀 快速图标访问测试"
echo "===================="

# 创建测试图标
TEST_ICON="/data/storage/uploads/icons/quick_test.png"
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
chmod 644 "$TEST_ICON"
chown caddy:caddy "$TEST_ICON" 2>/dev/null || true

echo "✅ 测试图标已创建: $TEST_ICON"

# 测试URL
TEST_URL="https://ios.xxyx.cn/storage/uploads/icons/quick_test.png"
echo "🌐 测试URL: $TEST_URL"

# 使用curl测试
echo ""
echo "📡 CURL测试结果:"
curl -I --max-time 10 --insecure "$TEST_URL"

echo ""
echo "📋 如果看到HTTP/1.1 200 OK，说明配置正确"
echo "📋 如果看到404，说明Caddy配置有问题"
echo "📋 如果连接超时，说明防火墙或DNS问题"

# 清理
rm -f "$TEST_ICON"

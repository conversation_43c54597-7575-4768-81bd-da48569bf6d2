<?php

/**
 * 数据库迁移脚本：将 distribution_url 重命名为 install_url
 * 
 * 使用方法：php scripts/migrate-distribution-url.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\ResignRecord;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔄 数据库迁移：distribution_url → install_url\n";
echo "==========================================\n\n";

try {
    $resignRecordModel = new ResignRecord();
    
    // 获取反射对象
    $reflection = new ReflectionClass($resignRecordModel);
    $collectionProperty = $reflection->getProperty('collection');
    $collectionProperty->setAccessible(true);
    $collection = $collectionProperty->getValue($resignRecordModel);
    
    // 查找所有有 distribution_url 的记录
    $cursor = $collection->find([
        'distribution_url' => ['$exists' => true, '$ne' => null]
    ]);
    
    $migratedCount = 0;
    $totalCount = 0;
    
    foreach ($cursor as $record) {
        $totalCount++;
        
        echo "处理记录: " . $record['_id'] . "\n";
        echo "  应用名称: " . ($record['app_name'] ?? '未知') . "\n";
        echo "  原 distribution_url: " . ($record['distribution_url'] ?? 'null') . "\n";
        
        // 检查是否已有 install_url
        if (isset($record['install_url'])) {
            echo "  已有 install_url，跳过迁移\n";
        } else {
            // 迁移 distribution_url 到 install_url
            $updateResult = $collection->updateOne(
                ['_id' => $record['_id']],
                [
                    '$set' => ['install_url' => $record['distribution_url']],
                    '$unset' => ['distribution_url' => '']
                ]
            );
            
            if ($updateResult->getModifiedCount() > 0) {
                echo "  ✅ 迁移成功\n";
                $migratedCount++;
            } else {
                echo "  ❌ 迁移失败\n";
            }
        }
        
        echo "  ---\n";
    }
    
    echo "\n🎯 迁移完成！\n";
    echo "总记录数: $totalCount\n";
    echo "迁移成功: $migratedCount\n";
    echo "跳过记录: " . ($totalCount - $migratedCount) . "\n\n";
    
    if ($migratedCount > 0) {
        echo "✅ 数据库迁移成功！\n";
        echo "现在所有分发链接都使用统一的 install_url 字段\n\n";
        
        echo "📋 验证迁移结果:\n";
        
        // 验证迁移结果
        $verifyCount = $collection->countDocuments([
            'install_url' => ['$exists' => true, '$ne' => null]
        ]);
        
        $oldFieldCount = $collection->countDocuments([
            'distribution_url' => ['$exists' => true, '$ne' => null]
        ]);
        
        echo "有 install_url 的记录: $verifyCount\n";
        echo "仍有 distribution_url 的记录: $oldFieldCount\n";
        
        if ($oldFieldCount === 0) {
            echo "✅ 所有旧字段已清理完成\n";
        } else {
            echo "⚠️  仍有 $oldFieldCount 个记录包含旧字段\n";
        }
        
    } else {
        echo "ℹ️  没有需要迁移的记录\n";
        echo "可能的原因:\n";
        echo "1. 所有记录已经使用 install_url 字段\n";
        echo "2. 没有包含分发链接的记录\n";
        echo "3. 迁移已经执行过了\n";
    }
    
    echo "\n🚀 下一步:\n";
    echo "1. 同步代码到服务器\n";
    echo "2. 测试前端分发功能\n";
    echo "3. 验证新的重签任务使用正确的字段名\n";
    
} catch (\Exception $e) {
    echo "❌ 迁移过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n💡 提示:\n";
echo "- 此脚本是安全的，可以重复执行\n";
echo "- 迁移过程会保留原始数据的完整性\n";
echo "- 如有问题，可以手动回滚数据\n";

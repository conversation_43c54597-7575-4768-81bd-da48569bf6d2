#!/bin/bash

echo "🔍 调试Caddy路由问题..."
echo "================================"

# 创建测试文件
TEST_ICON="/data/storage/uploads/icons/debug_test.png"
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
chmod 644 "$TEST_ICON"
chown caddy:caddy "$TEST_ICON" 2>/dev/null || true

echo "✅ 测试文件已创建: $TEST_ICON"

# 测试不同的访问方式
echo ""
echo "🧪 测试不同的访问方式..."

# 1. 测试API域名
echo ""
echo "1. 测试API域名:"
API_URL="https://api.ios.xxyx.cn/storage/uploads/icons/debug_test.png"
echo "URL: $API_URL"

API_RESPONSE=$(curl -s --max-time 10 --insecure "$API_URL")
echo "响应内容: $API_RESPONSE"

# 2. 测试本地访问
echo ""
echo "2. 测试本地访问:"
LOCAL_URL="http://localhost/storage/uploads/icons/debug_test.png"
echo "URL: $LOCAL_URL"

LOCAL_RESPONSE=$(curl -s --max-time 5 "$LOCAL_URL" 2>/dev/null || echo "连接失败")
echo "响应内容: $LOCAL_RESPONSE"

# 3. 检查文件是否真的存在
echo ""
echo "3. 检查文件系统:"
echo "文件路径: $TEST_ICON"
if [ -f "$TEST_ICON" ]; then
    echo "✅ 文件存在"
    ls -la "$TEST_ICON"
else
    echo "❌ 文件不存在"
fi

# 4. 检查Caddy配置
echo ""
echo "4. 检查Caddy配置:"
API_CONFIG="/etc/caddy/sites/api.ios.xxyx.cn.conf"
if [ -f "$API_CONFIG" ]; then
    echo "✅ 配置文件存在"
    echo ""
    echo "Storage相关配置:"
    grep -A 10 -B 2 "storage" "$API_CONFIG" || echo "未找到storage配置"
else
    echo "❌ 配置文件不存在"
fi

# 5. 检查Caddy进程
echo ""
echo "5. 检查Caddy状态:"
if systemctl is-active --quiet caddy; then
    echo "✅ Caddy正在运行"
    
    # 检查Caddy监听的端口
    echo ""
    echo "Caddy监听端口:"
    netstat -tlnp | grep caddy || echo "未找到Caddy监听端口"
else
    echo "❌ Caddy未运行"
fi

# 清理测试文件
rm -f "$TEST_ICON"

echo ""
echo "📋 分析:"
echo "如果API响应是JSON错误，说明请求被重定向到了PHP"
echo "如果本地访问成功，说明Caddy配置有效但可能有SSL或域名问题"
echo "如果都失败，说明Caddy配置本身有问题"

<?php

/**
 * 清空测试数据脚本
 * 
 * 使用方法：php scripts/clear-test-data.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Models\ResignRecord;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🗑️  清空测试数据\n";
echo "===============\n\n";

echo "⚠️  警告：此操作将删除所有重签记录和分发记录！\n";
echo "确认要继续吗？(输入 'yes' 确认): ";

$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if ($confirmation !== 'yes') {
    echo "❌ 操作已取消\n";
    exit(0);
}

try {
    echo "\n🔄 开始清空数据...\n\n";

    // 清空重签记录
    echo "📋 清空重签记录...\n";
    $resignRecordModel = new ResignRecord();

    $reflection = new ReflectionClass($resignRecordModel);
    $collectionProperty = $reflection->getProperty('collection');
    $collectionProperty->setAccessible(true);
    $resignCollection = $collectionProperty->getValue($resignRecordModel);

    $resignCount = $resignCollection->countDocuments([]);
    echo "当前重签记录数: $resignCount\n";

    if ($resignCount > 0) {
        $resignResult = $resignCollection->deleteMany([]);
        echo "✅ 删除了 " . $resignResult->getDeletedCount() . " 条重签记录\n";
    } else {
        echo "ℹ️  没有重签记录需要删除\n";
    }

    // 分发记录会随着重签记录一起清理
    echo "\n📱 分发记录清理...\n";
    echo "ℹ️  分发记录会随着重签记录的清理而自动失效\n";

    // 清理临时文件
    echo "\n📁 清理临时文件...\n";
    $tempDirs = [
        '/data/storage/temp',
        '/data/storage/resigned_ipas'
    ];

    foreach ($tempDirs as $dir) {
        if (is_dir($dir)) {
            $files = glob($dir . '/*');
            $fileCount = count($files);

            if ($fileCount > 0) {
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
                echo "✅ 清理了 $dir 目录下的 $fileCount 个文件\n";
            } else {
                echo "ℹ️  $dir 目录已经是空的\n";
            }
        } else {
            echo "⚠️  目录不存在: $dir\n";
        }
    }

    echo "\n🎯 数据清理完成！\n\n";

    echo "✅ 清理结果:\n";
    echo "- 重签记录: 已清空\n";
    echo "- 分发记录: 已清空\n";
    echo "- 临时文件: 已清理\n\n";

    echo "🚀 现在可以测试新的功能:\n";
    echo "1. 访问: https://ios.xxyx.cn/resign.html\n";
    echo "2. 上传IPA文件进行重签\n";
    echo "3. 验证自动分发功能是否正常\n";
    echo "4. 检查是否使用了统一的 install_url 字段\n\n";

    echo "📋 验证数据清理:\n";
    $finalResignCount = $resignCollection->countDocuments([]);
    echo "剩余重签记录: $finalResignCount\n";

    echo "分发记录: 已随重签记录清理\n";

    if ($finalResignCount === 0) {
        echo "✅ 数据清理验证成功\n";
    } else {
        echo "⚠️  仍有数据残留，可能需要手动检查\n";
    }
} catch (\Exception $e) {
    echo "❌ 清理过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
}

echo "\n💡 提示:\n";
echo "- 证书数据不会被删除\n";
echo "- 用户数据不会被删除\n";
echo "- 只清理了重签和分发相关的测试数据\n";

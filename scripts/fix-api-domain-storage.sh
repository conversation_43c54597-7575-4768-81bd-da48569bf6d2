#!/bin/bash

echo "🔧 修复API域名的存储文件访问..."
echo "================================"

# 备份API配置
API_CONFIG="/etc/caddy/sites/api.ios.xxyx.cn.conf"
BACKUP_CONFIG="${API_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"

if [ -f "$API_CONFIG" ]; then
    cp "$API_CONFIG" "$BACKUP_CONFIG"
    echo "✅ API配置已备份到: $BACKUP_CONFIG"
else
    echo "❌ API配置文件不存在: $API_CONFIG"
    exit 1
fi

# 创建修复后的API配置
cat > "$API_CONFIG" << 'EOF'
api.ios.xxyx.cn {
    # 请求体大小限制
    request_body {
        max_size 2GB
    }

    # 正确的根目录 - 指向public目录
    root * /data/www/api.ios.xxyx.cn/public

    # 存储文件访问 - 最高优先级，使用handle指令避免被try_files拦截
    handle /storage/* {
        root * /data
        file_server {
            hide .env .git .htaccess .sql .bak
        }
        header Cache-Control "public, max-age=86400"
    }

    # API路由处理 - 只对非存储路径生效
    handle {
        try_files {path} /index.php
        php_fastcgi unix//run/php/php8.3-fpm.sock
    }

    # 文件服务（用于其他静态文件）
    file_server {
        hide .env .git .htaccess .sql .bak
    }

    # CORS头设置
    header {
        Access-Control-Allow-Origin "*"
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        -Server
    }
    
    # 处理OPTIONS预检请求
    @options method OPTIONS
    respond @options 200
    
    # 启用压缩
    encode gzip zstd
    
    # 日志记录
    log {
        output file /data/logs/access/api.ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

echo "✅ API配置已更新"

# 验证配置
echo ""
echo "🔍 验证Caddy配置..."
if caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ 配置语法正确"
    
    # 重新加载配置
    echo ""
    echo "🔄 重新加载Caddy配置..."
    if systemctl reload caddy; then
        echo "✅ Caddy配置重新加载成功"
        
        # 等待配置生效
        sleep 3
        
        # 测试修复效果
        echo ""
        echo "🧪 测试API域名存储访问..."
        
        # 创建测试文件
        TEST_ICON="/data/storage/uploads/icons/api_fix_test.png"
        echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
        chmod 644 "$TEST_ICON"
        chown caddy:caddy "$TEST_ICON" 2>/dev/null || true
        
        # 测试API域名访问
        API_TEST_URL="https://api.ios.xxyx.cn/storage/uploads/icons/api_fix_test.png"
        echo "测试URL: $API_TEST_URL"
        
        echo ""
        echo "📡 完整响应:"
        RESPONSE=$(curl -s -I --max-time 10 --insecure "$API_TEST_URL")
        echo "$RESPONSE"
        
        # 提取关键信息
        HTTP_CODE=$(echo "$RESPONSE" | head -1 | cut -d' ' -f2)
        CONTENT_TYPE=$(echo "$RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        
        echo ""
        echo "📊 结果分析:"
        echo "HTTP状态码: $HTTP_CODE"
        echo "Content-Type: $CONTENT_TYPE"
        
        if [ "$HTTP_CODE" = "200" ] && echo "$CONTENT_TYPE" | grep -q "image"; then
            echo ""
            echo "🎉 修复成功！"
            echo "✅ API域名现在可以正确访问存储文件"
            echo "✅ Content-Type正确显示为图片类型"
            
            # 测试实际的图标文件
            echo ""
            echo "🔍 测试实际图标文件..."
            ACTUAL_ICON_URL="https://api.ios.xxyx.cn/storage/uploads/icons/resign_68a8395e569d80105500f6c2.png"
            echo "实际图标URL: $ACTUAL_ICON_URL"
            
            ACTUAL_RESPONSE=$(curl -s -I --max-time 10 --insecure "$ACTUAL_ICON_URL")
            ACTUAL_HTTP_CODE=$(echo "$ACTUAL_RESPONSE" | head -1 | cut -d' ' -f2)
            
            if [ "$ACTUAL_HTTP_CODE" = "200" ]; then
                echo "✅ 实际图标文件也可以正常访问"
            else
                echo "⚠️  实际图标文件访问失败 (HTTP $ACTUAL_HTTP_CODE)"
                echo "可能是文件不存在，请检查文件路径"
            fi
            
        elif [ "$HTTP_CODE" = "200" ]; then
            echo ""
            echo "⚠️  HTTP 200但Content-Type不正确"
            echo "可能仍然被重定向到PHP处理"
            echo "Content-Type: $CONTENT_TYPE"
        else
            echo ""
            echo "❌ 修复失败 (HTTP $HTTP_CODE)"
            echo "恢复备份配置..."
            cp "$BACKUP_CONFIG" "$API_CONFIG"
            systemctl reload caddy
        fi
        
        # 清理测试文件
        rm -f "$TEST_ICON"
        
    else
        echo "❌ Caddy配置重新加载失败，恢复备份"
        cp "$BACKUP_CONFIG" "$API_CONFIG"
    fi
else
    echo "❌ 配置语法错误，恢复备份"
    cp "$BACKUP_CONFIG" "$API_CONFIG"
fi

echo ""
echo "📋 修复说明:"
echo "1. 添加了 route /storage/* 指令，优先处理存储文件请求"
echo "2. 存储文件请求不会被 try_files 重定向到 index.php"
echo "3. 保持了原有的API功能不变"
echo ""
echo "📋 如果修复成功:"
echo "1. 图标URL现在应该能正常访问"
echo "2. 重新测试重签功能，图标应该能正常显示"
echo "3. 如果浏览器仍显示旧的错误，请清除缓存"

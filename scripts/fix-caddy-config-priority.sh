#!/bin/bash

echo "🔧 修复Caddy配置优先级问题..."
echo "================================"

# 备份原配置
CADDY_CONFIG="/etc/caddy/sites/ios.xxyx.cn.conf"
BACKUP_CONFIG="${CADDY_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"

if [ -f "$CADDY_CONFIG" ]; then
    cp "$CADDY_CONFIG" "$BACKUP_CONFIG"
    echo "✅ 配置已备份到: $BACKUP_CONFIG"
else
    echo "❌ 配置文件不存在: $CADDY_CONFIG"
    exit 1
fi

# 创建修复后的配置
cat > "$CADDY_CONFIG" << 'EOF'
ios.xxyx.cn {
    root * /data/www/ios.xxyx.cn

    # 存储文件访问 - 使用更高优先级的handle指令
    handle /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
        header Cache-Control "public, max-age=86400"
    }

    # PHP文件处理
    @php path *.php
    handle @php {
        php_fastcgi unix//run/php/php8.3-fpm.sock
    }

    # 静态资源处理
    handle {
        # 智能路由处理 - 只对非存储路径生效
        try_files {path} {path}/index.html /index.html

        # 文件服务
        file_server {
            hide .htaccess .env .git .sql .bak
        }
    }

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 静态资源缓存
    @static {
        file
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"

    # 日志记录
    log {
        output file /data/logs/access/ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

echo "✅ 新配置已写入"

# 验证配置
echo ""
echo "🔍 验证Caddy配置..."
if caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ 配置语法正确"
    
    # 重新加载配置
    echo ""
    echo "🔄 重新加载Caddy配置..."
    if systemctl reload caddy; then
        echo "✅ Caddy配置重新加载成功"
        
        # 等待配置生效
        sleep 2
        
        # 测试修复效果
        echo ""
        echo "🧪 测试修复效果..."
        
        # 创建测试文件
        TEST_ICON="/data/storage/uploads/icons/fix_test.png"
        echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
        chmod 644 "$TEST_ICON"
        chown caddy:caddy "$TEST_ICON" 2>/dev/null || true
        
        # 测试访问
        TEST_URL="https://ios.xxyx.cn/storage/uploads/icons/fix_test.png"
        echo "测试URL: $TEST_URL"
        
        RESPONSE=$(curl -s -I --max-time 10 --insecure "$TEST_URL")
        CONTENT_TYPE=$(echo "$RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        HTTP_CODE=$(echo "$RESPONSE" | head -1 | cut -d' ' -f2)
        
        echo "HTTP状态码: $HTTP_CODE"
        echo "Content-Type: $CONTENT_TYPE"
        
        if [ "$HTTP_CODE" = "200" ] && echo "$CONTENT_TYPE" | grep -q "image"; then
            echo "🎉 修复成功！图标现在应该可以正常显示了"
        else
            echo "❌ 修复失败，恢复备份配置..."
            cp "$BACKUP_CONFIG" "$CADDY_CONFIG"
            systemctl reload caddy
        fi
        
        # 清理测试文件
        rm -f "$TEST_ICON"
        
    else
        echo "❌ Caddy配置重新加载失败，恢复备份"
        cp "$BACKUP_CONFIG" "$CADDY_CONFIG"
    fi
else
    echo "❌ 配置语法错误，恢复备份"
    cp "$BACKUP_CONFIG" "$CADDY_CONFIG"
fi

echo ""
echo "📋 如果问题仍然存在，请检查:"
echo "1. 浏览器缓存 - 强制刷新页面 (Ctrl+F5)"
echo "2. CDN缓存 - 如果使用了CDN，清除缓存"
echo "3. DNS缓存 - 清除本地DNS缓存"

<?php

/**
 * 测试密码自动检测功能
 * 
 * 使用方法：php scripts/test-password-detection.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\CertificateService;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "🔍 测试密码自动检测功能\n";
echo "======================\n\n";

try {
    // 获取证书对
    $certificateService = new CertificateService();
    $certificatePair = $certificateService->getBestCertificatePair();
    
    if (!$certificatePair) {
        echo "❌ 没有找到可用的证书对\n";
        exit(1);
    }
    
    echo "📋 找到证书对:\n";
    echo "P12证书: " . $certificatePair['p12']['name'] . "\n";
    echo "mobileprovision: " . $certificatePair['mobileprovision']['name'] . "\n";
    echo "P12文件路径: " . $certificatePair['p12']['file_path'] . "\n\n";
    
    // 检查是否有密码字段
    if (isset($certificatePair['p12']['password'])) {
        echo "✅ 证书对中包含密码字段\n";
        echo "密码: " . $certificatePair['p12']['password'] . "\n\n";
        
        // 验证密码是否正确
        $testCommand = 'openssl pkcs12 -info -in ' . escapeshellarg($certificatePair['p12']['file_path']) . 
                      ' -passin pass:' . escapeshellarg($certificatePair['p12']['password']) . ' -noout -legacy 2>&1';
        
        $testOutput = shell_exec($testCommand);
        $testSuccess = (strpos($testOutput, 'MAC verified OK') !== false || 
                       strpos($testOutput, 'Certificate') !== false);
        
        if ($testSuccess) {
            echo "✅ 密码验证成功\n";
        } else {
            echo "❌ 密码验证失败\n";
            echo "错误输出: " . trim($testOutput) . "\n";
        }
    } else {
        echo "⚠️  证书对中没有密码字段，开始自动检测...\n\n";
        
        // 测试常见密码
        $commonPasswords = ['123456', 'password', 'test', 'testpass123', ''];
        $p12Path = $certificatePair['p12']['file_path'];
        $correctPassword = null;
        
        foreach ($commonPasswords as $testPassword) {
            echo "测试密码: '$testPassword'\n";
            
            $testCommand = 'openssl pkcs12 -info -in ' . escapeshellarg($p12Path) . 
                          ' -passin pass:' . escapeshellarg($testPassword) . ' -noout -legacy 2>&1';
            
            $testOutput = shell_exec($testCommand);
            $testSuccess = (strpos($testOutput, 'MAC verified OK') !== false || 
                           strpos($testOutput, 'Certificate') !== false);
            
            if ($testSuccess) {
                $correctPassword = $testPassword;
                echo "✅ 找到正确密码: '$testPassword'\n";
                break;
            } else {
                echo "❌ 密码错误\n";
            }
        }
        
        if ($correctPassword !== null) {
            echo "\n🎉 密码自动检测成功！\n";
            echo "正确密码: '$correctPassword'\n\n";
            
            echo "💡 建议: 运行修复脚本更新数据库中的证书记录\n";
            echo "php scripts/fix-certificate-password.php\n";
        } else {
            echo "\n❌ 密码自动检测失败\n";
            echo "建议: 重新上传证书并提供正确密码\n";
        }
    }
    
    echo "\n🎯 测试完成！\n";
    
    if ((isset($certificatePair['p12']['password']) && $testSuccess) || 
        (!isset($certificatePair['p12']['password']) && $correctPassword !== null)) {
        echo "\n🚀 重签功能现在应该可以正常工作了！\n";
        echo "可以通过前端页面测试: https://ios.xxyx.cn/resign.html\n";
    } else {
        echo "\n⚠️  重签功能可能仍然无法工作\n";
        echo "建议重新上传有效的P12证书\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
}

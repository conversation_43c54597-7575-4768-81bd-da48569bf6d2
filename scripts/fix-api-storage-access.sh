#!/bin/bash

echo "🔧 配置API域名的存储文件访问..."
echo "================================"

# 检查API配置文件
API_CONFIG="/etc/caddy/sites/api.ios.xxyx.cn.conf"
BACKUP_CONFIG="${API_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"

if [ -f "$API_CONFIG" ]; then
    cp "$API_CONFIG" "$BACKUP_CONFIG"
    echo "✅ API配置已备份到: $BACKUP_CONFIG"
else
    echo "❌ API配置文件不存在: $API_CONFIG"
    exit 1
fi

# 更新API配置，添加存储文件访问
cat > "$API_CONFIG" << 'EOF'
api.ios.xxyx.cn {
    root * /data/www/api.ios.xxyx.cn/public

    # 存储文件访问 - 最高优先级
    route /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
        header Cache-Control "public, max-age=86400"
    }

    # PHP处理
    php_fastcgi unix//run/php/php8.3-fpm.sock

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 日志记录
    log {
        output file /data/logs/access/api.ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

echo "✅ API配置已更新"

# 验证配置
echo ""
echo "🔍 验证Caddy配置..."
if caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ 配置语法正确"
    
    # 重新加载配置
    echo ""
    echo "🔄 重新加载Caddy配置..."
    if systemctl reload caddy; then
        echo "✅ Caddy配置重新加载成功"
        
        # 等待配置生效
        sleep 3
        
        # 测试API域名的存储访问
        echo ""
        echo "🧪 测试API域名存储访问..."
        
        # 创建测试文件
        TEST_ICON="/data/storage/uploads/icons/api_test.png"
        echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
        chmod 644 "$TEST_ICON"
        chown caddy:caddy "$TEST_ICON" 2>/dev/null || true
        
        # 测试API域名访问
        API_TEST_URL="https://api.ios.xxyx.cn/storage/uploads/icons/api_test.png"
        echo "测试API URL: $API_TEST_URL"
        
        echo ""
        echo "📡 API域名响应头:"
        API_RESPONSE=$(curl -s -I --max-time 10 --insecure "$API_TEST_URL")
        echo "$API_RESPONSE"
        
        API_CONTENT_TYPE=$(echo "$API_RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        API_HTTP_CODE=$(echo "$API_RESPONSE" | head -1 | cut -d' ' -f2)
        
        echo ""
        echo "API HTTP状态码: $API_HTTP_CODE"
        echo "API Content-Type: $API_CONTENT_TYPE"
        
        # 同时测试前端域名
        FRONTEND_TEST_URL="https://ios.xxyx.cn/storage/uploads/icons/api_test.png"
        echo ""
        echo "测试前端URL: $FRONTEND_TEST_URL"
        
        FRONTEND_RESPONSE=$(curl -s -I --max-time 10 --insecure "$FRONTEND_TEST_URL")
        FRONTEND_CONTENT_TYPE=$(echo "$FRONTEND_RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        FRONTEND_HTTP_CODE=$(echo "$FRONTEND_RESPONSE" | head -1 | cut -d' ' -f2)
        
        echo "前端 HTTP状态码: $FRONTEND_HTTP_CODE"
        echo "前端 Content-Type: $FRONTEND_CONTENT_TYPE"
        
        # 判断结果
        echo ""
        if [ "$API_HTTP_CODE" = "200" ] && echo "$API_CONTENT_TYPE" | grep -q "image"; then
            echo "🎉 API域名存储访问配置成功！"
            echo "✅ 建议使用API域名访问图标文件"
        elif [ "$FRONTEND_HTTP_CODE" = "200" ] && echo "$FRONTEND_CONTENT_TYPE" | grep -q "image"; then
            echo "🎉 前端域名存储访问正常！"
            echo "✅ 可以继续使用前端域名访问图标文件"
        else
            echo "❌ 两个域名都无法正确访问存储文件"
            echo "恢复备份配置..."
            cp "$BACKUP_CONFIG" "$API_CONFIG"
            systemctl reload caddy
        fi
        
        # 清理测试文件
        rm -f "$TEST_ICON"
        
    else
        echo "❌ Caddy配置重新加载失败，恢复备份"
        cp "$BACKUP_CONFIG" "$API_CONFIG"
    fi
else
    echo "❌ 配置语法错误，恢复备份"
    cp "$BACKUP_CONFIG" "$API_CONFIG"
fi

echo ""
echo "📋 总结:"
echo "1. 已修改代码使用API域名生成图标URL"
echo "2. 已配置API域名支持存储文件访问"
echo "3. 如果API域名访问成功，图标应该能正常显示"
echo "4. 如果仍有问题，请清除浏览器缓存后重试"

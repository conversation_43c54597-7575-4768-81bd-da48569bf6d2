#!/bin/bash

echo "🔍 检查图标文件..."
echo "================================"

# 检查图标目录
ICON_DIR="/data/storage/uploads/icons"

if [ ! -d "$ICON_DIR" ]; then
    echo "❌ 图标目录不存在: $ICON_DIR"
    exit 1
fi

echo "✅ 图标目录存在: $ICON_DIR"

# 列出图标文件
echo ""
echo "📁 图标文件列表:"
ls -la "$ICON_DIR"

# 统计PNG文件数量
PNG_COUNT=$(find "$ICON_DIR" -name "*.png" | wc -l)
echo ""
echo "📊 PNG文件数量: $PNG_COUNT"

if [ $PNG_COUNT -eq 0 ]; then
    echo "❌ 没有找到PNG图标文件"
    
    # 创建测试图标
    echo ""
    echo "🧪 创建测试图标..."
    TEST_ICON="$ICON_DIR/test_icon.png"
    
    # 创建1x1像素的PNG图片（base64编码）
    echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
    
    if [ -f "$TEST_ICON" ]; then
        chmod 644 "$TEST_ICON"
        chown caddy:caddy "$TEST_ICON"
        echo "✅ 测试图标创建成功: $TEST_ICON"
    else
        echo "❌ 测试图标创建失败"
    fi
else
    echo "✅ 找到 $PNG_COUNT 个PNG文件"
    
    # 显示最新的几个图标文件
    echo ""
    echo "📄 最新的图标文件:"
    find "$ICON_DIR" -name "*.png" -type f -exec ls -la {} \; | head -5
fi

# 测试图标访问
echo ""
echo "🌐 测试图标访问..."

# 找一个PNG文件进行测试
TEST_FILE=$(find "$ICON_DIR" -name "*.png" -type f | head -1)

if [ -n "$TEST_FILE" ]; then
    FILENAME=$(basename "$TEST_FILE")
    echo "测试文件: $FILENAME"
    
    # 测试不同的URL
    URLS=(
        "https://ios.xxyx.cn/storage/uploads/icons/$FILENAME"
        "http://ios.xxyx.cn/storage/uploads/icons/$FILENAME"
        "https://api.ios.xxyx.cn/storage/uploads/icons/$FILENAME"
    )
    
    for URL in "${URLS[@]}"; do
        echo ""
        echo "测试URL: $URL"
        
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" --max-time 5 --insecure "$URL")
        
        if [ "$HTTP_CODE" = "200" ]; then
            echo "✅ 成功 (HTTP $HTTP_CODE)"
        else
            echo "❌ 失败 (HTTP $HTTP_CODE)"
        fi
    done
    
    # 测试本地访问
    echo ""
    echo "🏠 测试本地访问..."
    LOCAL_URL="http://localhost/storage/uploads/icons/$FILENAME"
    echo "本地URL: $LOCAL_URL"
    
    LOCAL_CODE=$(curl -s -o /dev/null -w "%{http_code}" --max-time 5 "$LOCAL_URL")
    
    if [ "$LOCAL_CODE" = "200" ]; then
        echo "✅ 本地访问成功 (HTTP $LOCAL_CODE)"
    else
        echo "❌ 本地访问失败 (HTTP $LOCAL_CODE)"
    fi
    
else
    echo "❌ 没有找到可测试的PNG文件"
fi

echo ""
echo "🔧 Caddy服务状态:"
systemctl is-active caddy && echo "✅ Caddy正在运行" || echo "❌ Caddy未运行"

echo ""
echo "📋 建议检查项目:"
echo "1. Caddy配置是否正确加载"
echo "2. 防火墙设置"
echo "3. SSL证书配置"
echo "4. DNS解析"
echo "5. 浏览器缓存"

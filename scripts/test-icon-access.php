<?php

/**
 * 测试图标访问脚本
 * 验证图标文件是否可以通过Web访问
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Config\AppConfig;

function testIconAccess(): void
{
    echo "🧪 测试图标访问...\n";
    
    try {
        // 获取配置
        $config = AppConfig::getInstance();
        $iconDir = $config->get('icon_storage_path') ?? '/data/storage/uploads/icons';
        $appUrl = $config->get('app_url') ?? 'https://ios.xxyx.cn';
        
        echo "图标目录: {$iconDir}\n";
        echo "应用URL: {$appUrl}\n\n";
        
        // 创建测试图标文件
        $testIconPath = $iconDir . '/test_' . uniqid() . '.png';
        $testIconContent = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        
        if (!is_dir($iconDir)) {
            mkdir($iconDir, 0755, true);
        }
        
        if (file_put_contents($testIconPath, $testIconContent)) {
            chmod($testIconPath, 0644);
            echo "✅ 测试图标文件创建成功: {$testIconPath}\n";
        } else {
            echo "❌ 测试图标文件创建失败\n";
            return;
        }
        
        // 构建访问URL
        $iconFilename = basename($testIconPath);
        $iconUrl = $appUrl . '/storage/uploads/icons/' . $iconFilename;
        
        echo "图标URL: {$iconUrl}\n";
        
        // 测试HTTP访问
        echo "\n🌐 测试HTTP访问...\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $iconUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "❌ CURL错误: {$error}\n";
        } else {
            echo "HTTP状态码: {$httpCode}\n";
            
            if ($httpCode == 200) {
                echo "✅ 图标可以正常访问\n";
            } else {
                echo "❌ 图标访问失败\n";
                echo "响应头:\n{$response}\n";
            }
        }
        
        // 检查文件权限
        echo "\n🔐 检查文件权限...\n";
        $perms = fileperms($testIconPath);
        $octalPerms = substr(sprintf('%o', $perms), -4);
        echo "文件权限: {$octalPerms}\n";
        
        $owner = posix_getpwuid(fileowner($testIconPath));
        $group = posix_getgrgid(filegroup($testIconPath));
        echo "文件所有者: {$owner['name']}:{$group['name']}\n";
        
        // 检查目录权限
        $dirPerms = fileperms($iconDir);
        $dirOctalPerms = substr(sprintf('%o', $dirPerms), -4);
        echo "目录权限: {$dirOctalPerms}\n";
        
        $dirOwner = posix_getpwuid(fileowner($iconDir));
        $dirGroup = posix_getgrgid(filegroup($iconDir));
        echo "目录所有者: {$dirOwner['name']}:{$dirGroup['name']}\n";
        
        // 检查Web服务器进程
        echo "\n🔍 检查Web服务器进程...\n";
        $processes = shell_exec('ps aux | grep -E "(caddy|nginx|apache)" | grep -v grep');
        if ($processes) {
            echo "Web服务器进程:\n{$processes}\n";
        } else {
            echo "❌ 未找到Web服务器进程\n";
        }
        
        // 清理测试文件
        if (unlink($testIconPath)) {
            echo "🧹 测试文件清理完成\n";
        }
        
        echo "\n📋 解决方案建议:\n";
        echo "1. 如果HTTP状态码是404，需要配置Web服务器支持/storage路径访问\n";
        echo "2. 如果HTTP状态码是403，检查文件和目录权限\n";
        echo "3. 如果HTTP状态码是500，检查Web服务器错误日志\n";
        echo "4. 运行以下脚本修复配置:\n";
        echo "   - ./scripts/setup-storage-symlinks.sh (符号链接方案)\n";
        echo "   - ./scripts/update-caddy-config.sh (Caddy配置方案)\n";
        
    } catch (Exception $e) {
        echo "❌ 错误: " . $e->getMessage() . "\n";
    }
}

// 运行测试
testIconAccess();

#!/bin/bash

# 修复存储文件访问问题
# 解决 https://api.ios.xxyx.cn/storage/* 返回"接口不存在"的问题

set -e

echo "🔧 修复API存储文件访问问题..."
echo "================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    exit 1
fi

# 配置文件路径
API_CONFIG="/etc/caddy/sites/api.ios.xxyx.cn.conf"

# 备份当前配置
echo "📋 备份当前配置..."
if [ -f "$API_CONFIG" ]; then
    cp "$API_CONFIG" "${API_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ 配置已备份"
else
    echo "⚠️  配置文件不存在，将创建新配置"
fi

# 创建修复后的API配置
echo "🔧 更新API配置..."
cat > "$API_CONFIG" << 'EOF'
api.ios.xxyx.cn {
    # 请求体大小限制
    request_body {
        max_size 2GB
    }

    # 正确的根目录 - 指向public目录
    root * /data/www/api.ios.xxyx.cn/public

    # 存储文件访问 - 最高优先级，使用handle指令避免被try_files拦截
    handle /storage/* {
        root * /data
        file_server {
            hide .env .git .htaccess .sql .bak
        }
        header Cache-Control "public, max-age=86400"
        header Access-Control-Allow-Origin "*"
    }
    
    # API路由处理 - 只对非存储路径生效
    handle {
        try_files {path} /index.php
        php_fastcgi unix//run/php/php8.3-fpm.sock
    }
    
    # CORS头设置
    header {
        Access-Control-Allow-Origin "*"
        Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
        Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
        -Server
    }
    
    # 处理OPTIONS预检请求
    @options method OPTIONS
    respond @options 200
    
    # 启用压缩
    encode gzip zstd
    
    # 日志记录
    log {
        output file /data/logs/access/api.ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

echo "✅ API配置已更新"

# 修复存储目录权限
echo "🔐 修复存储目录权限..."
chown -R caddy:caddy /data/storage
chmod -R 755 /data/storage
echo "✅ 权限已修复"

# 验证配置
echo "🔍 验证Caddy配置..."
if caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ 配置语法正确"
else
    echo "❌ 配置语法错误，请检查"
    exit 1
fi

# 重新加载Caddy
echo "🔄 重新加载Caddy..."
if systemctl reload caddy; then
    echo "✅ Caddy已重新加载"
else
    echo "❌ Caddy重新加载失败"
    exit 1
fi

# 测试存储文件访问
echo "🧪 测试存储文件访问..."
sleep 2

# 检查是否有图标文件可以测试
ICON_FILE=$(ls /data/storage/uploads/icons/*.png 2>/dev/null | head -1)
if [ -n "$ICON_FILE" ]; then
    ICON_NAME=$(basename "$ICON_FILE")
    TEST_URL="https://api.ios.xxyx.cn/storage/uploads/icons/$ICON_NAME"
    
    echo "测试URL: $TEST_URL"
    
    # 使用curl测试
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL")
    
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ 存储文件访问正常 (HTTP $HTTP_CODE)"
    else
        echo "⚠️  存储文件访问异常 (HTTP $HTTP_CODE)"
        echo "请检查DNS解析和防火墙设置"
    fi
else
    echo "⚠️  没有找到图标文件进行测试"
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 修复内容："
echo "1. 更新了Caddy配置，使用handle指令处理/storage/*路径"
echo "2. 修复了存储目录权限"
echo "3. 重新加载了Caddy服务"
echo ""
echo "🔗 现在可以通过以下URL访问存储文件："
echo "https://api.ios.xxyx.cn/storage/uploads/icons/文件名.png"

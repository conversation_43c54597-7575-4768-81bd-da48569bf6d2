#!/bin/bash

# 修复图标显示问题的综合脚本
# 自动检测并修复图标无法显示的问题

set -e

echo "🔧 修复图标显示问题..."
echo "================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 检测Web服务器类型
detect_webserver() {
    if systemctl is-active --quiet caddy; then
        echo "caddy"
    elif systemctl is-active --quiet nginx; then
        echo "nginx"
    elif systemctl is-active --quiet apache2; then
        echo "apache2"
    else
        echo "unknown"
    fi
}

# 创建存储目录
setup_storage_directories() {
    echo "📁 设置存储目录..."
    
    mkdir -p /data/storage/uploads/icons
    mkdir -p /data/storage/uploads/ipa
    mkdir -p /data/storage/resigned_ipas
    
    # 检测Web服务器用户
    if id "caddy" &>/dev/null; then
        WEB_USER="caddy"
    elif id "www-data" &>/dev/null; then
        WEB_USER="www-data"
    elif id "nginx" &>/dev/null; then
        WEB_USER="nginx"
    else
        WEB_USER="root"
    fi
    
    echo "Web服务器用户: $WEB_USER"
    
    # 设置权限
    chown -R $WEB_USER:$WEB_USER /data/storage
    chmod -R 755 /data/storage
    
    echo "✅ 存储目录设置完成"
}

# 修复Caddy配置
fix_caddy_config() {
    echo "🔧 修复Caddy配置..."
    
    # 备份配置
    cp /etc/caddy/sites/ios.xxyx.cn.conf /etc/caddy/sites/ios.xxyx.cn.conf.backup.$(date +%Y%m%d_%H%M%S)
    
    # 更新配置
    cat > /etc/caddy/sites/ios.xxyx.cn.conf << 'EOF'
ios.xxyx.cn {
    root * /data/www/ios.xxyx.cn

    # 存储文件访问 - 优先处理
    handle_path /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
        header Cache-Control "public, max-age=86400"
    }

    # 智能路由处理
    try_files {path} {path}/index.html {path}/index.php /index.html

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # PHP处理
    @php path *.php
    php_fastcgi @php unix//run/php/php8.3-fpm.sock

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 静态资源缓存
    @static {
        file
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"

    # 日志记录
    log {
        output file /data/logs/access/ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

    # 验证并重新加载配置
    if caddy validate --config /etc/caddy/Caddyfile; then
        systemctl reload caddy
        echo "✅ Caddy配置更新成功"
    else
        echo "❌ Caddy配置验证失败，恢复备份"
        cp /etc/caddy/sites/ios.xxyx.cn.conf.backup.$(date +%Y%m%d_%H%M%S) /etc/caddy/sites/ios.xxyx.cn.conf
        return 1
    fi
}

# 创建测试文件
create_test_file() {
    echo "🧪 创建测试文件..."
    
    # 创建1x1像素的PNG图片
    local test_file="/data/storage/uploads/icons/test.png"
    echo -n -e '\x89\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90\x77\x53\xde\x00\x00\x00\x0c\x49\x44\x41\x54\x08\x99\x01\x01\x00\x00\x00\x00\x00\x37\x6e\xf9\x24\x00\x00\x00\x00\x49\x45\x4e\x44\xae\x42\x60\x82' > "$test_file"
    
    chown $WEB_USER:$WEB_USER "$test_file"
    chmod 644 "$test_file"
    
    echo "✅ 测试文件创建完成: $test_file"
}

# 测试访问
test_access() {
    echo "🌐 测试图标访问..."
    
    local test_url="https://ios.xxyx.cn/storage/uploads/icons/test.png"
    
    # 使用curl测试
    local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 --insecure "$test_url")
    
    echo "测试URL: $test_url"
    echo "HTTP状态码: $http_code"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ 图标访问测试成功"
        return 0
    else
        echo "❌ 图标访问测试失败"
        return 1
    fi
}

# 主执行流程
main() {
    echo "开始修复图标显示问题..."
    
    # 检测Web服务器
    WEBSERVER=$(detect_webserver)
    echo "检测到Web服务器: $WEBSERVER"
    
    # 设置存储目录
    setup_storage_directories
    
    # 根据Web服务器类型修复配置
    case $WEBSERVER in
        "caddy")
            fix_caddy_config
            ;;
        "nginx")
            echo "⚠️  检测到Nginx，请手动配置location规则支持/storage路径"
            echo "添加以下配置到nginx站点配置中:"
            echo "location /storage/ {"
            echo "    alias /data/storage/;"
            echo "    expires 1d;"
            echo "}"
            ;;
        "apache2")
            echo "⚠️  检测到Apache，请手动配置Alias支持/storage路径"
            ;;
        *)
            echo "❌ 未检测到支持的Web服务器"
            exit 1
            ;;
    esac
    
    # 创建测试文件
    create_test_file
    
    # 等待配置生效
    sleep 2
    
    # 测试访问
    if test_access; then
        echo ""
        echo "🎉 图标显示问题修复成功！"
        echo ""
        echo "📋 测试结果:"
        echo "- 存储目录: /data/storage/uploads/icons/"
        echo "- 访问URL: https://ios.xxyx.cn/storage/uploads/icons/test.png"
        echo "- 权限设置: 755 (目录) / 644 (文件)"
        echo "- 所有者: $WEB_USER:$WEB_USER"
        echo ""
        echo "✅ 现在图标应该可以正常显示了"
    else
        echo ""
        echo "❌ 图标访问仍然失败，请检查:"
        echo "1. Web服务器配置是否正确"
        echo "2. 防火墙设置"
        echo "3. SELinux设置 (如果启用)"
        echo "4. SSL证书配置"
        echo ""
        echo "🔍 调试信息:"
        echo "- 检查Web服务器错误日志"
        echo "- 运行: curl -I https://ios.xxyx.cn/storage/uploads/icons/test.png"
        echo "- 检查文件权限: ls -la /data/storage/uploads/icons/"
    fi
    
    # 清理测试文件
    rm -f /data/storage/uploads/icons/test.png
}

# 运行主函数
main

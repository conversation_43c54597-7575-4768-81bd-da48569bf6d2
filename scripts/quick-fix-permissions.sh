#!/bin/bash

# 快速修复存储目录权限问题
# 一键解决所有权限相关的错误

echo "🚀 快速修复存储目录权限..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行: sudo $0"
    exit 1
fi

# 存储目录
STORAGE_DIR="/data/storage"

# 检测PHP-FPM用户
PHP_FPM_USER=$(ps aux | grep php-fpm | grep -v root | head -1 | awk '{print $1}')
if [ -z "$PHP_FPM_USER" ]; then
    PHP_FPM_USER="www-data"  # 默认用户
fi

echo "PHP-FPM用户: $PHP_FPM_USER"

# 创建所有必要目录
echo "📁 创建目录..."
mkdir -p "$STORAGE_DIR/temp"
mkdir -p "$STORAGE_DIR/resigned_ipas"
mkdir -p "$STORAGE_DIR/uploads/ipa"
mkdir -p "$STORAGE_DIR/uploads/icons"
mkdir -p "$STORAGE_DIR/certificates"
mkdir -p "$STORAGE_DIR/plists"
mkdir -p "$STORAGE_DIR/qrcodes"

# 设置所有者和权限
echo "🔐 设置权限..."
chown -R "$PHP_FPM_USER:$PHP_FPM_USER" "$STORAGE_DIR"
chmod -R 755 "$STORAGE_DIR"

# 设置关键目录为777权限（确保写入权限）
chmod 777 "$STORAGE_DIR/temp"
chmod 777 "$STORAGE_DIR/uploads/icons"
chmod 777 "$STORAGE_DIR/uploads/ipa"
chmod 777 "$STORAGE_DIR/resigned_ipas"

# 重启PHP-FPM
echo "🔄 重启PHP-FPM..."
systemctl restart php8.3-fpm

# 快速测试
echo "🧪 快速测试..."
TEST_FILE="$STORAGE_DIR/temp/quick_test.txt"
if sudo -u "$PHP_FPM_USER" touch "$TEST_FILE" 2>/dev/null; then
    echo "✅ 权限修复成功！"
    rm -f "$TEST_FILE"
else
    echo "❌ 权限修复可能失败，请运行完整诊断脚本"
fi

echo "🎉 快速修复完成！"

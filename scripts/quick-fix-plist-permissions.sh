#!/bin/bash

# 快速修复plist权限问题
# 立即解决当前的plist文件生成权限错误

set -e

echo "🚀 快速修复plist权限问题..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 创建plist和qrcode目录
echo "📁 创建plist和qrcode目录..."
mkdir -p /data/storage/plists
mkdir -p /data/storage/qrcodes

# 检测Web服务器用户
if id "caddy" &>/dev/null; then
    WEB_USER="caddy"
    WEB_GROUP="caddy"
elif id "www-data" &>/dev/null; then
    WEB_USER="www-data"
    WEB_GROUP="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
    WEB_GROUP="nginx"
else
    WEB_USER="root"
    WEB_GROUP="root"
fi

echo "Web服务器用户: $WEB_USER:$WEB_GROUP"

# 设置权限
echo "🔐 设置目录权限..."
chown -R $WEB_USER:$WEB_GROUP /data/storage/plists
chown -R $WEB_USER:$WEB_GROUP /data/storage/qrcodes
chmod -R 775 /data/storage/plists
chmod -R 775 /data/storage/qrcodes

# 测试权限
echo "🧪 测试权限..."
test_file="/data/storage/plists/test_$(date +%s).plist"

if echo "test plist content" > "$test_file"; then
    echo "✅ plist文件创建测试成功"
    rm -f "$test_file"
else
    echo "❌ plist文件创建测试失败"
    exit 1
fi

# 显示目录信息
echo ""
echo "📋 目录权限信息:"
echo "================================"

for dir in "/data/storage/plists" "/data/storage/qrcodes"; do
    if [ -d "$dir" ]; then
        perms=$(stat -c "%a" "$dir" 2>/dev/null || echo "unknown")
        owner=$(stat -c "%U:%G" "$dir" 2>/dev/null || echo "unknown")
        echo "$dir - 权限: $perms, 所有者: $owner"
    fi
done

echo ""
echo "🎉 plist权限问题修复完成！"
echo ""
echo "✅ 现在可以正常生成plist文件了"
echo "✅ 重签功能的分发链接应该可以正常工作"
echo ""
echo "📋 测试建议:"
echo "1. 尝试重新提交一个重签任务"
echo "2. 检查是否能正常生成安装页面"
echo "3. 查看PHP错误日志确认无权限错误: tail -f /data/logs/php/error.log"

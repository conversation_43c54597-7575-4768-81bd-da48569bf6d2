<?php

/**
 * 权限检查脚本
 * 检查存储目录的权限和可写性
 */

function checkPermissions(): void
{
    echo "🔍 检查存储目录权限...\n";
    echo "================================\n";

    $directories = [
        '/data/storage' => '存储根目录',
        '/data/storage/uploads' => '上传目录',
        '/data/storage/uploads/ipa' => 'IPA文件目录',
        '/data/storage/uploads/icons' => '图标目录',
        '/data/storage/resigned_ipas' => '重签文件目录',
        '/data/storage/plists' => 'Plist文件目录',
        '/data/storage/temp' => '临时目录',
        '/tmp/ipa_uploads' => '临时上传目录'
    ];

    $allGood = true;

    foreach ($directories as $dir => $description) {
        echo "\n📁 检查 {$description}: {$dir}\n";

        // 检查目录是否存在
        if (!is_dir($dir)) {
            echo "❌ 目录不存在\n";
            $allGood = false;
            continue;
        }

        // 检查权限
        $perms = fileperms($dir);
        $octalPerms = substr(sprintf('%o', $perms), -4);
        echo "权限: {$octalPerms}\n";

        // 检查所有者
        $owner = posix_getpwuid(fileowner($dir));
        $group = posix_getgrgid(filegroup($dir));
        echo "所有者: {$owner['name']}:{$group['name']}\n";

        // 检查是否可读
        if (is_readable($dir)) {
            echo "✅ 可读\n";
        } else {
            echo "❌ 不可读\n";
            $allGood = false;
        }

        // 检查是否可写
        if (is_writable($dir)) {
            echo "✅ 可写\n";
        } else {
            echo "❌ 不可写\n";
            $allGood = false;
        }

        // 测试创建文件
        $testFile = $dir . '/test_' . uniqid() . '.txt';
        if (file_put_contents($testFile, 'test')) {
            echo "✅ 文件创建测试成功\n";
            unlink($testFile);
        } else {
            echo "❌ 文件创建测试失败\n";
            $allGood = false;
        }
    }

    echo "\n" . str_repeat("=", 50) . "\n";

    if ($allGood) {
        echo "🎉 所有目录权限检查通过！\n";
    } else {
        echo "❌ 发现权限问题，请运行修复脚本:\n";
        echo "sudo ./scripts/fix-storage-permissions.sh\n";
    }

    // 检查PHP进程信息
    echo "\n🔍 PHP进程信息:\n";
    echo "当前用户: " . get_current_user() . "\n";
    echo "进程用户ID: " . getmyuid() . "\n";
    echo "进程组ID: " . getmygid() . "\n";

    // 检查环境变量
    echo "\n🔍 环境变量:\n";
    echo "IPA_STORAGE_PATH: " . ($_ENV['IPA_STORAGE_PATH'] ?? '未设置') . "\n";
    echo "ICON_STORAGE_PATH: " . ($_ENV['ICON_STORAGE_PATH'] ?? '未设置') . "\n";

    // 检查磁盘空间
    echo "\n💾 磁盘空间:\n";
    $freeBytes = disk_free_space('/data');
    $totalBytes = disk_total_space('/data');
    $usedBytes = $totalBytes - $freeBytes;

    echo "总空间: " . formatBytes($totalBytes) . "\n";
    echo "已使用: " . formatBytes($usedBytes) . "\n";
    echo "可用空间: " . formatBytes($freeBytes) . "\n";

    if ($freeBytes < 1024 * 1024 * 1024) { // 小于1GB
        echo "⚠️  磁盘空间不足！\n";
    }
}

function formatBytes(int $bytes): string
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= (1 << (10 * $pow));

    return round($bytes, 2) . ' ' . $units[$pow];
}

// 运行检查
checkPermissions();

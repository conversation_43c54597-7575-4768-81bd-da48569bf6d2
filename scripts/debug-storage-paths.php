<?php

// 调试存储路径配置
// 检查为什么会出现双斜杠

require_once __DIR__ . '/../vendor/autoload.php';

use App\Config\AppConfig;

echo "🔍 调试存储路径配置...\n";
echo "================================\n";

// 获取配置实例
$config = AppConfig::getInstance();

// 检查所有相关配置
$configs = [
    'app_storage_path',
    'storage_path', 
    'ipa_storage_path',
    'icon_storage_path',
    'upload_tmp_path'
];

echo "📋 当前配置值:\n";
foreach ($configs as $key) {
    $value = $config->get($key);
    echo sprintf("%-20s: %s\n", $key, $value ?? 'NULL');
}

echo "\n";

// 检查环境变量
echo "📋 环境变量:\n";
$envVars = [
    'APP_STORAGE_PATH',
    'STORAGE_PATH',
    'IPA_STORAGE_PATH', 
    'ICON_STORAGE_PATH',
    'UPLOAD_TMP_PATH'
];

foreach ($envVars as $var) {
    $value = $_ENV[$var] ?? getenv($var);
    echo sprintf("%-20s: %s\n", $var, $value ?: 'NOT SET');
}

echo "\n";

// 模拟ResignService的路径构建
echo "🔧 模拟路径构建:\n";
$basePath = $config->get('app_storage_path') ?? '/data/storage/';
echo "原始 basePath: '$basePath'\n";

$trimmedBasePath = rtrim($basePath, '/');
echo "rtrim后 basePath: '$trimmedBasePath'\n";

$resignStoragePath = $trimmedBasePath . '/resigned_ipas';
echo "resignStoragePath: '$resignStoragePath'\n";

$tempPath = $trimmedBasePath . '/temp';
echo "tempPath: '$tempPath'\n";

echo "\n";

// 检查实际目录
echo "📁 检查实际目录:\n";
$directories = [
    '/data/storage',
    '/data/storage/resigned_ipas',
    '/data/storage/temp',
    '/data/storage/uploads/icons'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $owner = posix_getpwuid(fileowner($dir))['name'] ?? 'unknown';
        $group = posix_getgrgid(filegroup($dir))['name'] ?? 'unknown';
        echo sprintf("✅ %-30s (权限: %s, 所有者: %s:%s)\n", $dir, $perms, $owner, $group);
    } else {
        echo sprintf("❌ %-30s (不存在)\n", $dir);
    }
}

echo "\n";

// 检查可能的双斜杠问题
echo "🔍 检查双斜杠问题:\n";
$testPaths = [
    $basePath . '/resigned_ipas',
    $basePath . 'resigned_ipas',
    rtrim($basePath, '/') . '/resigned_ipas'
];

foreach ($testPaths as $i => $path) {
    $hasDoubleSlash = strpos($path, '//') !== false;
    echo sprintf("测试路径 %d: '%s' %s\n", $i+1, $path, $hasDoubleSlash ? '❌ 有双斜杠' : '✅ 正常');
}

echo "\n";
echo "🎯 建议修复:\n";
if (strpos($basePath, '//') !== false) {
    echo "❌ basePath本身包含双斜杠，需要修复环境变量\n";
} elseif (substr($basePath, -1) === '/' && strpos($resignStoragePath, '//') !== false) {
    echo "❌ basePath以斜杠结尾导致双斜杠，rtrim()应该已经处理\n";
    echo "可能是环境变量中有多个斜杠\n";
} else {
    echo "✅ 路径配置看起来正常\n";
}

<?php

/**
 * 调试图标访问问题
 * 检查图标文件和URL访问
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Config\AppConfig;

function debugIconAccess(): void
{
    echo "🔍 调试图标访问问题...\n";
    echo "================================\n";
    
    try {
        // 获取配置
        $config = AppConfig::getInstance();
        $iconDir = $config->get('icon_storage_path') ?? '/data/storage/uploads/icons';
        $appUrl = $config->get('app_url') ?? 'https://ios.xxyx.cn';
        
        echo "图标存储目录: {$iconDir}\n";
        echo "应用URL: {$appUrl}\n\n";
        
        // 检查目录是否存在
        if (!is_dir($iconDir)) {
            echo "❌ 图标目录不存在: {$iconDir}\n";
            return;
        }
        
        echo "✅ 图标目录存在\n";
        
        // 列出目录中的文件
        echo "\n📁 图标目录内容:\n";
        $files = scandir($iconDir);
        $iconFiles = array_filter($files, function($file) {
            return pathinfo($file, PATHINFO_EXTENSION) === 'png';
        });
        
        if (empty($iconFiles)) {
            echo "❌ 图标目录中没有PNG文件\n";
            
            // 创建一个测试图标
            echo "\n🧪 创建测试图标...\n";
            $testIconPath = $iconDir . '/test_icon.png';
            
            // 创建1x1像素的PNG图片
            $pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
            
            if (file_put_contents($testIconPath, $pngData)) {
                chmod($testIconPath, 0644);
                echo "✅ 测试图标创建成功: {$testIconPath}\n";
                $iconFiles = ['test_icon.png'];
            } else {
                echo "❌ 测试图标创建失败\n";
                return;
            }
        }
        
        foreach ($iconFiles as $file) {
            if ($file === '.' || $file === '..') continue;
            
            $filePath = $iconDir . '/' . $file;
            $fileSize = filesize($filePath);
            $perms = substr(sprintf('%o', fileperms($filePath)), -4);
            $owner = posix_getpwuid(fileowner($filePath));
            $group = posix_getgrgid(filegroup($filePath));
            
            echo "  📄 {$file} - 大小: {$fileSize}字节, 权限: {$perms}, 所有者: {$owner['name']}:{$group['name']}\n";
        }
        
        // 测试第一个图标文件的访问
        $testFile = reset($iconFiles);
        if ($testFile) {
            $iconUrl = $appUrl . '/storage/uploads/icons/' . $testFile;
            echo "\n🌐 测试图标访问:\n";
            echo "图标URL: {$iconUrl}\n";
            
            // 使用curl测试访问
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $iconUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            $effectiveUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
            curl_close($ch);
            
            if ($error) {
                echo "❌ CURL错误: {$error}\n";
            } else {
                echo "HTTP状态码: {$httpCode}\n";
                echo "最终URL: {$effectiveUrl}\n";
                
                if ($httpCode == 200) {
                    echo "✅ 图标可以正常访问\n";
                } else {
                    echo "❌ 图标访问失败\n";
                    echo "响应头:\n";
                    $headers = explode("\n", $response);
                    foreach ($headers as $header) {
                        if (trim($header)) {
                            echo "  {$header}\n";
                        }
                    }
                }
            }
            
            // 测试直接文件访问
            echo "\n📂 测试直接文件访问:\n";
            $directUrl = $appUrl . '/storage/uploads/icons/' . $testFile;
            echo "直接URL: {$directUrl}\n";
            
            // 测试不同的URL格式
            $testUrls = [
                $appUrl . '/storage/uploads/icons/' . $testFile,
                'https://api.ios.xxyx.cn/storage/uploads/icons/' . $testFile,
                $appUrl . '/data/storage/uploads/icons/' . $testFile,
            ];
            
            foreach ($testUrls as $testUrl) {
                echo "\n测试URL: {$testUrl}\n";
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $testUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HEADER, true);
                curl_setopt($ch, CURLOPT_NOBODY, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 5);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode == 200) {
                    echo "✅ 成功 (HTTP {$httpCode})\n";
                } else {
                    echo "❌ 失败 (HTTP {$httpCode})\n";
                }
            }
        }
        
        // 检查Caddy配置
        echo "\n🔧 Caddy配置检查:\n";
        echo "根据您的配置，URL映射应该是:\n";
        echo "  URL: /storage/uploads/icons/filename.png\n";
        echo "  映射到: /data/storage/uploads/icons/filename.png\n";
        echo "  这个映射看起来是正确的。\n";
        
        // 检查可能的问题
        echo "\n🔍 可能的问题:\n";
        echo "1. Caddy服务是否正在运行？\n";
        echo "2. Caddy配置是否已重新加载？\n";
        echo "3. 防火墙是否阻止了访问？\n";
        echo "4. SSL证书是否有问题？\n";
        echo "5. DNS解析是否正确？\n";
        
        // 提供解决建议
        echo "\n💡 解决建议:\n";
        echo "1. 重新加载Caddy配置: sudo systemctl reload caddy\n";
        echo "2. 检查Caddy状态: sudo systemctl status caddy\n";
        echo "3. 查看Caddy日志: sudo journalctl -u caddy -f\n";
        echo "4. 测试本地访问: curl -I http://localhost/storage/uploads/icons/{$testFile}\n";
        echo "5. 检查防火墙: sudo ufw status\n";
        
    } catch (Exception $e) {
        echo "❌ 错误: " . $e->getMessage() . "\n";
    }
}

// 运行调试
debugIconAccess();

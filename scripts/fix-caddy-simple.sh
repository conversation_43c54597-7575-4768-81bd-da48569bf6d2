#!/bin/bash

echo "🔧 简单修复Caddy存储访问问题..."
echo "================================"

# 备份原配置
CADDY_CONFIG="/etc/caddy/sites/ios.xxyx.cn.conf"
BACKUP_CONFIG="${CADDY_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"

if [ -f "$CADDY_CONFIG" ]; then
    cp "$CADDY_CONFIG" "$BACKUP_CONFIG"
    echo "✅ 配置已备份到: $BACKUP_CONFIG"
else
    echo "❌ 配置文件不存在: $CADDY_CONFIG"
    exit 1
fi

# 创建修复后的配置 - 使用更简单的方法
cat > "$CADDY_CONFIG" << 'EOF'
ios.xxyx.cn {
    root * /data/www/ios.xxyx.cn

    # 存储文件访问 - 最高优先级
    route /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
        header Cache-Control "public, max-age=86400"
    }

    # PHP处理
    @php path *.php
    php_fastcgi @php unix//run/php/php8.3-fpm.sock

    # 智能路由处理
    try_files {path} {path}/index.html {path}/index.php /index.html

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 静态资源缓存
    @static {
        file
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"

    # 日志记录
    log {
        output file /data/logs/access/ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

echo "✅ 新配置已写入"

# 验证配置
echo ""
echo "🔍 验证Caddy配置..."
if caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ 配置语法正确"
    
    # 重新加载配置
    echo ""
    echo "🔄 重新加载Caddy配置..."
    if systemctl reload caddy; then
        echo "✅ Caddy配置重新加载成功"
        
        # 等待配置生效
        sleep 3
        
        # 测试修复效果
        echo ""
        echo "🧪 测试修复效果..."
        
        # 创建测试文件
        TEST_ICON="/data/storage/uploads/icons/route_test.png"
        echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_ICON"
        chmod 644 "$TEST_ICON"
        chown caddy:caddy "$TEST_ICON" 2>/dev/null || true
        
        # 测试访问
        TEST_URL="https://ios.xxyx.cn/storage/uploads/icons/route_test.png"
        echo "测试URL: $TEST_URL"
        
        echo ""
        echo "📡 完整响应头:"
        RESPONSE=$(curl -s -I --max-time 10 --insecure "$TEST_URL")
        echo "$RESPONSE"
        
        CONTENT_TYPE=$(echo "$RESPONSE" | grep -i "content-type" | cut -d' ' -f2- | tr -d '\r')
        HTTP_CODE=$(echo "$RESPONSE" | head -1 | cut -d' ' -f2)
        
        echo ""
        echo "HTTP状态码: $HTTP_CODE"
        echo "Content-Type: $CONTENT_TYPE"
        
        if [ "$HTTP_CODE" = "200" ] && echo "$CONTENT_TYPE" | grep -q "image"; then
            echo ""
            echo "🎉 修复成功！图标现在应该可以正常显示了"
            echo "✅ Content-Type正确显示为图片类型"
        elif [ "$HTTP_CODE" = "200" ]; then
            echo ""
            echo "⚠️  HTTP 200但Content-Type仍然不正确"
            echo "这可能是浏览器缓存问题，请清除缓存后重试"
        else
            echo ""
            echo "❌ 修复失败，恢复备份配置..."
            cp "$BACKUP_CONFIG" "$CADDY_CONFIG"
            systemctl reload caddy
        fi
        
        # 清理测试文件
        rm -f "$TEST_ICON"
        
    else
        echo "❌ Caddy配置重新加载失败，恢复备份"
        cp "$BACKUP_CONFIG" "$CADDY_CONFIG"
    fi
else
    echo "❌ 配置语法错误，恢复备份"
    cp "$BACKUP_CONFIG" "$CADDY_CONFIG"
fi

echo ""
echo "📋 如果修复成功但浏览器仍显示不了图标:"
echo "1. 强制刷新页面 (Ctrl+F5 或 Cmd+Shift+R)"
echo "2. 清除浏览器缓存"
echo "3. 尝试无痕模式访问"
echo "4. 检查浏览器开发者工具的网络面板"

#!/bin/bash

# 修复临时目录权限问题
# 解决 "Permission denied" 错误

set -e

echo "🔧 修复临时目录权限问题..."
echo "================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    exit 1
fi

# 检查PHP-FPM运行用户
echo "🔍 检查PHP-FPM配置..."
PHP_FPM_USER=$(ps aux | grep php-fpm | grep -v root | head -1 | awk '{print $1}')
if [ -n "$PHP_FPM_USER" ]; then
    echo "✅ PHP-FPM运行用户: $PHP_FPM_USER"
else
    echo "⚠️  无法检测PHP-FPM运行用户，假设为www-data"
    PHP_FPM_USER="www-data"
fi

# 检查存储目录
STORAGE_DIR="/data/storage"
if [ ! -d "$STORAGE_DIR" ]; then
    echo "❌ 存储目录不存在: $STORAGE_DIR"
    exit 1
fi

echo "📁 当前存储目录权限:"
ls -la "$STORAGE_DIR"

# 修复目录权限
echo ""
echo "🔐 修复目录权限..."

# 确保所有存储目录存在
mkdir -p "$STORAGE_DIR/temp"
mkdir -p "$STORAGE_DIR/resigned_ipas"
mkdir -p "$STORAGE_DIR/uploads/ipa"
mkdir -p "$STORAGE_DIR/uploads/icons"
mkdir -p "$STORAGE_DIR/certificates"
mkdir -p "$STORAGE_DIR/plists"
mkdir -p "$STORAGE_DIR/qrcodes"

# 设置目录所有者为PHP-FPM用户
chown -R "$PHP_FPM_USER:$PHP_FPM_USER" "$STORAGE_DIR"

# 设置目录权限（755 = rwxr-xr-x）
chmod -R 755 "$STORAGE_DIR"

# 特别设置需要写入权限的目录
chmod 777 "$STORAGE_DIR/temp"
chmod 777 "$STORAGE_DIR/uploads/icons"
chmod 777 "$STORAGE_DIR/uploads/ipa"
chmod 777 "$STORAGE_DIR/resigned_ipas"

echo "✅ 权限修复完成"

# 显示修复后的权限
echo ""
echo "📋 修复后的目录权限:"
ls -la "$STORAGE_DIR"

echo ""
echo "📋 temp目录权限:"
ls -la "$STORAGE_DIR/temp"

# 测试权限
echo ""
echo "🧪 测试权限..."

# 测试关键目录的写入权限
TEST_DIRS=("temp" "uploads/icons" "uploads/ipa" "resigned_ipas")

for dir in "${TEST_DIRS[@]}"; do
    TEST_FILE="$STORAGE_DIR/$dir/test_$(date +%s).txt"
    echo "测试目录: $dir"

    if sudo -u "$PHP_FPM_USER" touch "$TEST_FILE" 2>/dev/null; then
        echo "✅ PHP-FPM用户可以在 $dir 目录创建文件"
        rm -f "$TEST_FILE"
    else
        echo "❌ PHP-FPM用户无法在 $dir 目录创建文件"
        echo "尝试设置更宽松的权限..."
        chmod 777 "$STORAGE_DIR/$dir"

        if sudo -u "$PHP_FPM_USER" touch "$TEST_FILE" 2>/dev/null; then
            echo "✅ 权限修复成功"
            rm -f "$TEST_FILE"
        else
            echo "❌ 权限修复失败，请检查SELinux或其他安全策略"
        fi
    fi
    echo ""
done

# 检查PHP配置
echo ""
echo "🐘 检查PHP配置..."

# 检查upload_tmp_dir权限
UPLOAD_TMP_DIR=$(php -r "echo ini_get('upload_tmp_dir') ?: sys_get_temp_dir();")
echo "PHP上传临时目录: $UPLOAD_TMP_DIR"

if [ -d "$UPLOAD_TMP_DIR" ]; then
    echo "临时目录权限:"
    ls -la "$UPLOAD_TMP_DIR" | head -3
    
    # 测试PHP用户是否能访问上传临时目录
    if sudo -u "$PHP_FPM_USER" ls "$UPLOAD_TMP_DIR" >/dev/null 2>&1; then
        echo "✅ PHP-FPM用户可以访问上传临时目录"
    else
        echo "⚠️  PHP-FPM用户无法访问上传临时目录"
    fi
else
    echo "⚠️  上传临时目录不存在"
fi

# 重启PHP-FPM服务
echo ""
echo "🔄 重启PHP-FPM服务..."
if systemctl restart php8.3-fpm; then
    echo "✅ PHP-FPM服务重启成功"
else
    echo "❌ PHP-FPM服务重启失败"
fi

echo ""
echo "🎉 权限修复完成！"
echo ""
echo "📋 修复内容："
echo "1. 修复了存储目录权限"
echo "2. 设置PHP-FPM用户为目录所有者"
echo "3. 设置temp目录为777权限"
echo "4. 重启了PHP-FPM服务"
echo ""
echo "💡 如果问题仍然存在，请检查："
echo "1. SELinux设置: sestatus"
echo "2. PHP错误日志: tail -f /var/log/php8.3-fpm.log"
echo "3. 系统日志: journalctl -u php8.3-fpm -f"

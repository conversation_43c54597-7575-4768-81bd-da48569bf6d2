#!/bin/bash

# 修复双斜杠路径问题
# 解决 /data/storage//resigned_ipas 这样的路径问题

echo "🔧 修复双斜杠路径问题..."
echo "================================"

# 检查.env文件
ENV_FILE="/data/www/api.ios.xxyx.cn/.env"

if [ -f "$ENV_FILE" ]; then
    echo "📋 检查.env文件中的路径配置..."
    
    # 显示当前配置
    echo "当前配置:"
    grep -E "(STORAGE_PATH|APP_STORAGE_PATH)" "$ENV_FILE" || echo "未找到存储路径配置"
    
    echo ""
    
    # 备份.env文件
    cp "$ENV_FILE" "${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ .env文件已备份"
    
    # 修复路径配置
    echo "🔧 修复路径配置..."
    
    # 移除或修复可能的双斜杠配置
    sed -i 's|APP_STORAGE_PATH=/data/storage//*|APP_STORAGE_PATH=/data/storage|g' "$ENV_FILE"
    sed -i 's|STORAGE_PATH=/data/storage//*|STORAGE_PATH=/data/storage|g' "$ENV_FILE"
    
    # 如果没有APP_STORAGE_PATH配置，添加正确的配置
    if ! grep -q "APP_STORAGE_PATH" "$ENV_FILE"; then
        echo "APP_STORAGE_PATH=/data/storage" >> "$ENV_FILE"
        echo "✅ 添加APP_STORAGE_PATH配置"
    fi
    
    echo "修复后的配置:"
    grep -E "(STORAGE_PATH|APP_STORAGE_PATH)" "$ENV_FILE" || echo "未找到存储路径配置"
    
else
    echo "⚠️  .env文件不存在，创建基本配置..."
    
    # 创建基本的.env配置
    cat > "$ENV_FILE" << 'EOF'
# 存储路径配置
APP_STORAGE_PATH=/data/storage
STORAGE_PATH=/data/storage
IPA_STORAGE_PATH=/data/storage/uploads/ipa
ICON_STORAGE_PATH=/data/storage/uploads/icons
UPLOAD_TMP_PATH=/data/storage/uploads/upload_tmp
EOF
    
    echo "✅ 创建了基本的.env配置"
fi

echo ""

# 重启PHP-FPM以重新加载环境变量
echo "🔄 重启PHP-FPM..."
if systemctl restart php8.3-fpm; then
    echo "✅ PHP-FPM重启成功"
else
    echo "❌ PHP-FPM重启失败"
fi

echo ""

# 测试路径构建
echo "🧪 测试路径构建..."
php -r "
require_once '/data/www/api.ios.xxyx.cn/vendor/autoload.php';
use App\Config\AppConfig;

\$config = AppConfig::getInstance();
\$basePath = \$config->get('app_storage_path') ?? '/data/storage/';
echo 'basePath: ' . \$basePath . PHP_EOL;

\$trimmedBasePath = rtrim(\$basePath, '/');
echo 'trimmed basePath: ' . \$trimmedBasePath . PHP_EOL;

\$resignStoragePath = \$trimmedBasePath . '/resigned_ipas';
echo 'resignStoragePath: ' . \$resignStoragePath . PHP_EOL;

if (strpos(\$resignStoragePath, '//') !== false) {
    echo '❌ 仍然存在双斜杠问题' . PHP_EOL;
} else {
    echo '✅ 路径正常' . PHP_EOL;
}
"

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 修复内容："
echo "1. 检查并修复了.env文件中的路径配置"
echo "2. 移除了可能的双斜杠"
echo "3. 重启了PHP-FPM服务"
echo ""
echo "💡 如果问题仍然存在，请运行调试脚本："
echo "php scripts/debug-storage-paths.php"

<?php

/**
 * 修复图标目录权限脚本
 * 确保图标存储目录存在并具有正确的权限
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Config\AppConfig;

function fixIconDirectoryPermissions(): void
{
    echo "开始修复图标目录权限...\n";
    
    try {
        // 获取配置
        $config = AppConfig::getInstance();
        $iconDir = $config->get('icon_storage_path') ?? '/data/storage/uploads/icons';
        
        echo "图标目录路径: {$iconDir}\n";
        
        // 检查目录是否存在
        if (!is_dir($iconDir)) {
            echo "目录不存在，正在创建...\n";
            
            // 创建目录
            if (mkdir($iconDir, 0755, true)) {
                echo "✅ 目录创建成功\n";
            } else {
                echo "❌ 目录创建失败\n";
                return;
            }
        } else {
            echo "✅ 目录已存在\n";
        }
        
        // 检查目录权限
        $perms = fileperms($iconDir);
        $octalPerms = substr(sprintf('%o', $perms), -4);
        echo "当前权限: {$octalPerms}\n";
        
        // 设置正确的权限
        if (chmod($iconDir, 0755)) {
            echo "✅ 权限设置成功 (0755)\n";
        } else {
            echo "❌ 权限设置失败\n";
        }
        
        // 检查是否可写
        if (is_writable($iconDir)) {
            echo "✅ 目录可写\n";
        } else {
            echo "❌ 目录不可写\n";
        }
        
        // 创建测试文件
        $testFile = $iconDir . '/test_' . uniqid() . '.txt';
        if (file_put_contents($testFile, 'test')) {
            echo "✅ 测试文件创建成功\n";
            
            // 删除测试文件
            if (unlink($testFile)) {
                echo "✅ 测试文件删除成功\n";
            } else {
                echo "⚠️ 测试文件删除失败\n";
            }
        } else {
            echo "❌ 测试文件创建失败\n";
        }
        
        // 检查父目录权限
        $parentDir = dirname($iconDir);
        if (is_dir($parentDir)) {
            $parentPerms = fileperms($parentDir);
            $parentOctalPerms = substr(sprintf('%o', $parentPerms), -4);
            echo "父目录权限: {$parentOctalPerms}\n";
            
            if (is_writable($parentDir)) {
                echo "✅ 父目录可写\n";
            } else {
                echo "❌ 父目录不可写\n";
            }
        }
        
        echo "\n图标目录权限修复完成！\n";
        
    } catch (Exception $e) {
        echo "❌ 错误: " . $e->getMessage() . "\n";
    }
}

// 运行修复
fixIconDirectoryPermissions();

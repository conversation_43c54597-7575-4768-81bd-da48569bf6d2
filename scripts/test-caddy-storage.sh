#!/bin/bash

echo "🔧 测试Caddy存储配置..."
echo "================================"

# 检查Caddy状态
echo "📊 Caddy服务状态:"
if systemctl is-active --quiet caddy; then
    echo "✅ Caddy正在运行"
else
    echo "❌ Caddy未运行"
    echo "启动Caddy: sudo systemctl start caddy"
    exit 1
fi

# 检查Caddy配置
echo ""
echo "🔍 检查Caddy配置..."

CADDY_CONFIG="/etc/caddy/sites/ios.xxyx.cn.conf"
if [ -f "$CADDY_CONFIG" ]; then
    echo "✅ 配置文件存在: $CADDY_CONFIG"
    
    # 检查storage配置
    if grep -q "handle_path /storage/\*" "$CADDY_CONFIG"; then
        echo "✅ 找到storage路径配置"
        
        # 显示storage相关配置
        echo ""
        echo "📋 Storage配置内容:"
        grep -A 10 "handle_path /storage/\*" "$CADDY_CONFIG"
    else
        echo "❌ 未找到storage路径配置"
    fi
else
    echo "❌ 配置文件不存在: $CADDY_CONFIG"
fi

# 验证配置语法
echo ""
echo "🔍 验证Caddy配置语法..."
if caddy validate --config /etc/caddy/Caddyfile; then
    echo "✅ Caddy配置语法正确"
else
    echo "❌ Caddy配置语法错误"
fi

# 创建测试文件
echo ""
echo "🧪 创建测试文件..."

TEST_DIR="/data/storage/uploads/icons"
TEST_FILE="$TEST_DIR/caddy_test.png"

mkdir -p "$TEST_DIR"

# 创建测试PNG文件
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > "$TEST_FILE"

if [ -f "$TEST_FILE" ]; then
    chmod 644 "$TEST_FILE"
    chown caddy:caddy "$TEST_FILE"
    echo "✅ 测试文件创建成功: $TEST_FILE"
else
    echo "❌ 测试文件创建失败"
    exit 1
fi

# 测试访问
echo ""
echo "🌐 测试文件访问..."

# 测试本地访问
echo "测试本地访问..."
LOCAL_RESPONSE=$(curl -s -I "http://localhost/storage/uploads/icons/caddy_test.png")
LOCAL_CODE=$(echo "$LOCAL_RESPONSE" | head -1 | cut -d' ' -f2)

echo "本地访问响应码: $LOCAL_CODE"

if [ "$LOCAL_CODE" = "200" ]; then
    echo "✅ 本地访问成功"
else
    echo "❌ 本地访问失败"
    echo "响应头:"
    echo "$LOCAL_RESPONSE"
fi

# 测试外部访问
echo ""
echo "测试外部访问..."
EXTERNAL_URLS=(
    "https://ios.xxyx.cn/storage/uploads/icons/caddy_test.png"
    "http://ios.xxyx.cn/storage/uploads/icons/caddy_test.png"
)

for URL in "${EXTERNAL_URLS[@]}"; do
    echo ""
    echo "测试URL: $URL"
    
    RESPONSE=$(curl -s -I --max-time 10 --insecure "$URL")
    CODE=$(echo "$RESPONSE" | head -1 | cut -d' ' -f2)
    
    echo "响应码: $CODE"
    
    if [ "$CODE" = "200" ]; then
        echo "✅ 访问成功"
    else
        echo "❌ 访问失败"
        echo "响应头:"
        echo "$RESPONSE" | head -5
    fi
done

# 检查防火墙
echo ""
echo "🔥 检查防火墙状态..."
if command -v ufw >/dev/null 2>&1; then
    UFW_STATUS=$(ufw status | head -1)
    echo "UFW状态: $UFW_STATUS"
    
    if echo "$UFW_STATUS" | grep -q "active"; then
        echo "防火墙规则:"
        ufw status | grep -E "(80|443)"
    fi
else
    echo "UFW未安装"
fi

# 检查端口监听
echo ""
echo "🔌 检查端口监听..."
echo "端口80:"
netstat -tlnp | grep ":80 " || echo "端口80未监听"

echo "端口443:"
netstat -tlnp | grep ":443 " || echo "端口443未监听"

# 清理测试文件
echo ""
echo "🧹 清理测试文件..."
rm -f "$TEST_FILE"
echo "✅ 测试文件已删除"

echo ""
echo "📋 故障排除建议:"
echo "1. 如果本地访问成功但外部访问失败，检查防火墙和DNS"
echo "2. 如果本地访问也失败，检查Caddy配置和文件权限"
echo "3. 重新加载Caddy配置: sudo systemctl reload caddy"
echo "4. 查看Caddy日志: sudo journalctl -u caddy -f"
echo "5. 检查SSL证书: sudo caddy list-certificates"

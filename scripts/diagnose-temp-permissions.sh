#!/bin/bash

# 诊断临时目录权限问题
# 分析 "Permission denied" 错误的原因

echo "🔍 诊断临时目录权限问题..."
echo "================================"

# 1. 检查存储目录结构
echo "📁 检查存储目录结构..."
STORAGE_DIR="/data/storage"

if [ -d "$STORAGE_DIR" ]; then
    echo "✅ 存储目录存在: $STORAGE_DIR"
    echo "📋 目录权限:"
    ls -la "$STORAGE_DIR"
    
    echo ""
    echo "📋 temp目录详情:"
    if [ -d "$STORAGE_DIR/temp" ]; then
        ls -la "$STORAGE_DIR/temp"
    else
        echo "❌ temp目录不存在"
    fi
else
    echo "❌ 存储目录不存在: $STORAGE_DIR"
fi

echo ""

# 2. 检查PHP-FPM配置
echo "🐘 检查PHP-FPM配置..."

# 检查PHP-FPM进程
echo "📋 PHP-FPM进程:"
ps aux | grep php-fpm | grep -v grep

# 检查PHP-FPM运行用户
PHP_FPM_USER=$(ps aux | grep php-fpm | grep -v root | head -1 | awk '{print $1}')
if [ -n "$PHP_FPM_USER" ]; then
    echo "✅ PHP-FPM运行用户: $PHP_FPM_USER"
else
    echo "❌ 无法检测PHP-FPM运行用户"
fi

# 检查PHP-FPM配置文件
PHP_FPM_CONF="/etc/php/8.3/fpm/pool.d/www.conf"
if [ -f "$PHP_FPM_CONF" ]; then
    echo "📋 PHP-FPM池配置:"
    grep -E "^(user|group)" "$PHP_FPM_CONF" || echo "未找到用户配置"
else
    echo "⚠️  PHP-FPM配置文件不存在: $PHP_FPM_CONF"
fi

echo ""

# 3. 检查PHP上传配置
echo "📤 检查PHP上传配置..."

# 检查上传临时目录
UPLOAD_TMP_DIR=$(php -r "echo ini_get('upload_tmp_dir') ?: sys_get_temp_dir();")
echo "PHP上传临时目录: $UPLOAD_TMP_DIR"

if [ -d "$UPLOAD_TMP_DIR" ]; then
    echo "📋 上传临时目录权限:"
    ls -la "$UPLOAD_TMP_DIR" | head -3
else
    echo "❌ 上传临时目录不存在"
fi

# 检查PHP配置
echo "📋 相关PHP配置:"
php -r "
echo 'upload_max_filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;
echo 'post_max_size: ' . ini_get('post_max_size') . PHP_EOL;
echo 'max_file_uploads: ' . ini_get('max_file_uploads') . PHP_EOL;
echo 'file_uploads: ' . (ini_get('file_uploads') ? 'On' : 'Off') . PHP_EOL;
"

echo ""

# 4. 测试权限
echo "🧪 测试权限..."

if [ -n "$PHP_FPM_USER" ] && [ -d "$STORAGE_DIR/temp" ]; then
    echo "测试PHP-FPM用户在temp目录的权限..."
    
    TEST_FILE="$STORAGE_DIR/temp/permission_test_$(date +%s).txt"
    
    if sudo -u "$PHP_FPM_USER" touch "$TEST_FILE" 2>/dev/null; then
        echo "✅ PHP-FPM用户可以在temp目录创建文件"
        sudo -u "$PHP_FPM_USER" rm -f "$TEST_FILE"
    else
        echo "❌ PHP-FPM用户无法在temp目录创建文件"
        echo "错误详情:"
        sudo -u "$PHP_FPM_USER" touch "$TEST_FILE" 2>&1 || true
    fi
else
    echo "⚠️  无法进行权限测试（缺少用户信息或目录）"
fi

echo ""

# 5. 检查系统安全策略
echo "🔒 检查系统安全策略..."

# 检查SELinux
if command -v sestatus >/dev/null 2>&1; then
    echo "📋 SELinux状态:"
    sestatus
    
    if sestatus | grep -q "SELinux status:.*enabled"; then
        echo "⚠️  SELinux已启用，可能影响文件权限"
        echo "相关SELinux上下文:"
        ls -Z "$STORAGE_DIR" 2>/dev/null || echo "无法获取SELinux上下文"
    fi
else
    echo "✅ 系统未安装SELinux"
fi

# 检查AppArmor
if command -v aa-status >/dev/null 2>&1; then
    echo "📋 AppArmor状态:"
    aa-status 2>/dev/null | head -5 || echo "AppArmor未运行"
else
    echo "✅ 系统未安装AppArmor"
fi

echo ""

# 6. 分析问题
echo "🎯 问题分析..."
echo "================================"

ISSUES_FOUND=0

# 检查路径问题
if [ -f "/data/www/api.ios.xxyx.cn/src/Services/ResignService.php" ]; then
    if grep -q "/data/storage//" "/data/www/api.ios.xxyx.cn/src/Services/ResignService.php"; then
        echo "❌ 发现路径问题：代码中存在双斜杠路径"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
fi

# 检查目录权限
if [ -d "$STORAGE_DIR/temp" ]; then
    TEMP_PERMS=$(stat -c "%a" "$STORAGE_DIR/temp")
    if [ "$TEMP_PERMS" -lt 755 ]; then
        echo "❌ temp目录权限不足: $TEMP_PERMS"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
fi

# 检查所有者
if [ -d "$STORAGE_DIR" ] && [ -n "$PHP_FPM_USER" ]; then
    STORAGE_OWNER=$(stat -c "%U" "$STORAGE_DIR")
    if [ "$STORAGE_OWNER" != "$PHP_FPM_USER" ]; then
        echo "❌ 存储目录所有者不匹配: $STORAGE_OWNER (应为 $PHP_FPM_USER)"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
fi

if [ $ISSUES_FOUND -eq 0 ]; then
    echo "✅ 未发现明显的权限问题"
else
    echo "⚠️  发现 $ISSUES_FOUND 个潜在问题"
fi

echo ""
echo "💡 建议解决方案："
echo "1. 运行权限修复脚本: sudo ./scripts/fix-temp-permissions.sh"
echo "2. 检查PHP错误日志: tail -f /var/log/php8.3-fpm.log"
echo "3. 如果问题持续，重启相关服务: sudo systemctl restart php8.3-fpm caddy"

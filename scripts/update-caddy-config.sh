#!/bin/bash

# 更新Caddy配置以支持存储目录访问

set -e

echo "🔧 更新Caddy配置..."

# 备份原配置
cp /etc/caddy/sites/ios.xxyx.cn.conf /etc/caddy/sites/ios.xxyx.cn.conf.backup

# 更新前端配置，添加存储目录访问
cat > /etc/caddy/sites/ios.xxyx.cn.conf << 'EOF'
ios.xxyx.cn {
    root * /data/www/ios.xxyx.cn

    # 存储文件访问
    handle_path /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
    }

    # 智能路由处理
    try_files {path} {path}/index.html {path}/index.php /index.html

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # PHP处理
    @php path *.php
    php_fastcgi @php unix//run/php/php8.3-fpm.sock

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 静态资源缓存
    @static {
        file
        path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"

    # 日志记录
    log {
        output file /data/logs/access/ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

# 同样更新API配置
cp /etc/caddy/sites/api.ios.xxyx.cn.conf /etc/caddy/sites/api.ios.xxyx.cn.conf.backup

cat > /etc/caddy/sites/api.ios.xxyx.cn.conf << 'EOF'
api.ios.xxyx.cn {
    root * /data/www/api.ios.xxyx.cn/public

    # 存储文件访问
    handle_path /storage/* {
        root * /data
        file_server {
            hide .htaccess .env .git .sql .bak
        }
    }

    # PHP处理
    php_fastcgi unix//run/php/php8.3-fpm.sock

    # 文件服务
    file_server {
        hide .htaccess .env .git .sql .bak
    }

    # 启用压缩
    encode gzip zstd

    # 安全头设置
    header {
        -Server
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        Cache-Control "public, max-age=3600"
    }

    # 日志记录
    log {
        output file /data/logs/access/api.ios.xxyx.cn.log {
            roll_size 100mb
            roll_keep 10
        }
        format json
    }
}
EOF

# 验证配置
echo "🔍 验证Caddy配置..."
caddy validate --config /etc/caddy/Caddyfile

if [ $? -eq 0 ]; then
    echo "✅ Caddy配置验证成功"
    
    # 重新加载配置
    echo "🔄 重新加载Caddy配置..."
    systemctl reload caddy
    
    if [ $? -eq 0 ]; then
        echo "✅ Caddy配置重新加载成功"
    else
        echo "❌ Caddy配置重新加载失败"
        exit 1
    fi
else
    echo "❌ Caddy配置验证失败"
    echo "🔄 恢复备份配置..."
    cp /etc/caddy/sites/ios.xxyx.cn.conf.backup /etc/caddy/sites/ios.xxyx.cn.conf
    cp /etc/caddy/sites/api.ios.xxyx.cn.conf.backup /etc/caddy/sites/api.ios.xxyx.cn.conf
    exit 1
fi

# 创建存储目录
echo "📁 确保存储目录存在..."
mkdir -p /data/storage/uploads/icons
mkdir -p /data/storage/uploads/ipa
mkdir -p /data/storage/resigned_ipas

# 设置权限
echo "🔐 设置目录权限..."
chown -R caddy:caddy /data/storage
chmod -R 755 /data/storage

# 创建测试文件
echo "🧪 创建测试文件..."
echo "test icon" > /data/storage/uploads/icons/test.png
chown caddy:caddy /data/storage/uploads/icons/test.png
chmod 644 /data/storage/uploads/icons/test.png

echo ""
echo "🎉 Caddy配置更新完成！"
echo ""
echo "📋 访问测试："
echo "- 前端图标访问: https://ios.xxyx.cn/storage/uploads/icons/test.png"
echo "- API图标访问: https://api.ios.xxyx.cn/storage/uploads/icons/test.png"
echo ""
echo "📁 存储目录："
echo "- 图标目录: /data/storage/uploads/icons/"
echo "- IPA目录: /data/storage/uploads/ipa/"
echo "- 重签目录: /data/storage/resigned_ipas/"
echo ""
echo "⚠️  注意事项："
echo "1. 图标URL格式: https://ios.xxyx.cn/storage/uploads/icons/filename.png"
echo "2. 如果仍然无法访问，检查文件权限和SELinux设置"
echo "3. 可以通过 'curl -I https://ios.xxyx.cn/storage/uploads/icons/test.png' 测试访问"

#!/bin/bash

# 修复存储目录权限脚本
# 解决文件复制权限问题

set -e

echo "🔧 修复存储目录权限问题..."
echo "================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 创建所有必要的存储目录
create_storage_directories() {
    echo "📁 创建存储目录..."
    
    # 主存储目录
    mkdir -p /data/storage
    mkdir -p /data/storage/uploads
    mkdir -p /data/storage/uploads/icons
    mkdir -p /data/storage/uploads/ipa
    mkdir -p /data/storage/resigned_ipas
    mkdir -p /data/storage/plists
    mkdir -p /data/storage/temp
    
    # 临时上传目录
    mkdir -p /tmp/ipa_uploads
    
    echo "✅ 存储目录创建完成"
}

# 设置正确的权限
set_permissions() {
    echo "🔐 设置目录权限..."
    
    # 检测Web服务器用户
    if id "caddy" &>/dev/null; then
        WEB_USER="caddy"
        WEB_GROUP="caddy"
    elif id "www-data" &>/dev/null; then
        WEB_USER="www-data"
        WEB_GROUP="www-data"
    elif id "nginx" &>/dev/null; then
        WEB_USER="nginx"
        WEB_GROUP="nginx"
    else
        WEB_USER="root"
        WEB_GROUP="root"
    fi
    
    echo "Web服务器用户: $WEB_USER:$WEB_GROUP"
    
    # 设置存储目录权限
    chown -R $WEB_USER:$WEB_GROUP /data/storage
    chmod -R 755 /data/storage
    
    # 上传目录需要写权限
    chmod -R 775 /data/storage/uploads
    chmod -R 775 /data/storage/temp
    chmod -R 775 /data/storage/resigned_ipas
    chmod -R 775 /data/storage/plists
    
    # 临时上传目录权限
    chown -R $WEB_USER:$WEB_GROUP /tmp/ipa_uploads
    chmod -R 775 /tmp/ipa_uploads
    
    echo "✅ 权限设置完成"
}

# 检查PHP进程用户
check_php_user() {
    echo "🔍 检查PHP进程用户..."
    
    # 检查PHP-FPM配置
    if [ -f "/etc/php/8.3/fpm/pool.d/www.conf" ]; then
        PHP_USER=$(grep "^user = " /etc/php/8.3/fpm/pool.d/www.conf | cut -d' ' -f3)
        PHP_GROUP=$(grep "^group = " /etc/php/8.3/fpm/pool.d/www.conf | cut -d' ' -f3)
        echo "PHP-FPM用户: $PHP_USER:$PHP_GROUP"
        
        # 如果PHP用户与Web用户不同，需要调整权限
        if [ "$PHP_USER" != "$WEB_USER" ]; then
            echo "⚠️  PHP用户与Web用户不同，调整权限..."
            
            # 将PHP用户添加到Web用户组
            usermod -a -G $WEB_GROUP $PHP_USER
            
            # 设置组写权限
            chmod -R g+w /data/storage/uploads
            chmod -R g+w /data/storage/temp
            chmod -R g+w /data/storage/resigned_ipas
            chmod -R g+w /data/storage/plists
            
            echo "✅ PHP用户权限调整完成"
        fi
    fi
}

# 测试权限
test_permissions() {
    echo "🧪 测试权限..."
    
    # 测试创建文件
    local test_file="/data/storage/uploads/ipa/test_$(date +%s).txt"
    
    if echo "test" > "$test_file"; then
        echo "✅ 文件创建测试成功"
        rm -f "$test_file"
    else
        echo "❌ 文件创建测试失败"
        return 1
    fi
    
    # 测试复制文件
    local source_file="/tmp/test_source_$(date +%s).txt"
    local target_file="/data/storage/uploads/ipa/test_copy_$(date +%s).txt"
    
    echo "test content" > "$source_file"
    
    if cp "$source_file" "$target_file"; then
        echo "✅ 文件复制测试成功"
        rm -f "$source_file" "$target_file"
    else
        echo "❌ 文件复制测试失败"
        rm -f "$source_file"
        return 1
    fi
}

# 修复环境变量配置
fix_env_config() {
    echo "🔧 检查环境变量配置..."
    
    local env_file="/data/www/api.ios.xxyx.cn/.env"
    
    if [ -f "$env_file" ]; then
        # 检查IPA存储路径配置
        if grep -q "IPA_STORAGE_PATH" "$env_file"; then
            echo "✅ IPA_STORAGE_PATH 已配置"
        else
            echo "IPA_STORAGE_PATH=/data/storage/uploads/ipa" >> "$env_file"
            echo "✅ 添加 IPA_STORAGE_PATH 配置"
        fi
        
        # 检查图标存储路径配置
        if grep -q "ICON_STORAGE_PATH" "$env_file"; then
            echo "✅ ICON_STORAGE_PATH 已配置"
        else
            echo "ICON_STORAGE_PATH=/data/storage/uploads/icons" >> "$env_file"
            echo "✅ 添加 ICON_STORAGE_PATH 配置"
        fi
        
        # 设置.env文件权限
        chown $WEB_USER:$WEB_GROUP "$env_file"
        chmod 640 "$env_file"
    else
        echo "⚠️  .env文件不存在: $env_file"
    fi
}

# 重启相关服务
restart_services() {
    echo "🔄 重启相关服务..."
    
    # 重启PHP-FPM
    if systemctl is-active --quiet php8.3-fpm; then
        systemctl restart php8.3-fpm
        echo "✅ PHP-FPM 重启完成"
    fi
    
    # 重启Caddy
    if systemctl is-active --quiet caddy; then
        systemctl restart caddy
        echo "✅ Caddy 重启完成"
    fi
    
    # 重启队列服务（如果存在）
    if systemctl is-active --quiet xios-queue; then
        systemctl restart xios-queue
        echo "✅ 队列服务 重启完成"
    fi
}

# 显示目录信息
show_directory_info() {
    echo ""
    echo "📋 目录权限信息:"
    echo "================================"
    
    for dir in "/data/storage" "/data/storage/uploads" "/data/storage/uploads/ipa" "/data/storage/uploads/icons" "/tmp/ipa_uploads"; do
        if [ -d "$dir" ]; then
            local perms=$(stat -c "%a" "$dir" 2>/dev/null || echo "unknown")
            local owner=$(stat -c "%U:%G" "$dir" 2>/dev/null || echo "unknown")
            echo "$dir - 权限: $perms, 所有者: $owner"
        fi
    done
}

# 主执行流程
main() {
    echo "开始修复存储目录权限问题..."
    
    # 创建目录
    create_storage_directories
    
    # 设置权限
    set_permissions
    
    # 检查PHP用户
    check_php_user
    
    # 修复环境变量
    fix_env_config
    
    # 测试权限
    if test_permissions; then
        echo ""
        echo "🎉 存储目录权限修复成功！"
    else
        echo ""
        echo "❌ 权限测试失败，请检查配置"
        show_directory_info
        exit 1
    fi
    
    # 重启服务
    restart_services
    
    # 显示目录信息
    show_directory_info
    
    echo ""
    echo "✅ 修复完成！现在应该可以正常上传和处理文件了"
    echo ""
    echo "📋 测试建议:"
    echo "1. 尝试上传一个IPA文件"
    echo "2. 检查文件是否成功保存到 /data/storage/uploads/ipa/"
    echo "3. 查看PHP错误日志: tail -f /data/logs/php/error.log"
    echo "4. 查看队列处理日志: journalctl -u xios-queue -f"
}

# 运行主函数
main

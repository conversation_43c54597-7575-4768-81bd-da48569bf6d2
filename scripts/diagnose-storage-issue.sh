#!/bin/bash

# 诊断存储文件访问问题
# 分析为什么 https://api.ios.xxyx.cn/storage/* 返回"接口不存在"

echo "🔍 诊断API存储文件访问问题..."
echo "================================"

# 1. 检查文件是否存在
echo "📁 检查目标文件..."
TARGET_FILE="/data/storage/uploads/icons/resign_68a8395e569d80105500f6c2.png"
if [ -f "$TARGET_FILE" ]; then
    echo "✅ 文件存在: $TARGET_FILE"
    echo "📋 文件信息:"
    ls -la "$TARGET_FILE"
else
    echo "❌ 文件不存在: $TARGET_FILE"
    echo "📋 目录内容:"
    ls -la /data/storage/uploads/icons/ | head -5
fi

echo ""

# 2. 检查Caddy配置
echo "🔧 检查Caddy配置..."
CADDY_CONFIG="/etc/caddy/sites/api.ios.xxyx.cn.conf"
if [ -f "$CADDY_CONFIG" ]; then
    echo "✅ 配置文件存在: $CADDY_CONFIG"
    
    # 检查是否有storage相关配置
    if grep -q "/storage/" "$CADDY_CONFIG"; then
        echo "✅ 找到storage路径配置"
        echo "📋 Storage配置:"
        grep -A 5 -B 2 "/storage/" "$CADDY_CONFIG"
    else
        echo "❌ 未找到storage路径配置"
    fi
    
    echo ""
    echo "📋 完整配置内容:"
    cat "$CADDY_CONFIG"
else
    echo "❌ 配置文件不存在: $CADDY_CONFIG"
fi

echo ""

# 3. 检查Caddy服务状态
echo "🔄 检查Caddy服务状态..."
if systemctl is-active --quiet caddy; then
    echo "✅ Caddy服务正在运行"
    
    # 检查配置语法
    if caddy validate --config /etc/caddy/Caddyfile 2>/dev/null; then
        echo "✅ Caddy配置语法正确"
    else
        echo "❌ Caddy配置语法错误"
        echo "错误详情:"
        caddy validate --config /etc/caddy/Caddyfile
    fi
else
    echo "❌ Caddy服务未运行"
    echo "服务状态:"
    systemctl status caddy --no-pager -l
fi

echo ""

# 4. 测试本地访问
echo "🌐 测试本地访问..."
if command -v curl >/dev/null 2>&1; then
    TEST_URL="https://api.ios.xxyx.cn/storage/uploads/icons/resign_68a8395e569d80105500f6c2.png"
    echo "测试URL: $TEST_URL"
    
    # 获取HTTP状态码
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$TEST_URL" 2>/dev/null || echo "ERROR")
    echo "HTTP状态码: $HTTP_CODE"
    
    if [ "$HTTP_CODE" != "200" ]; then
        echo "❌ 访问失败，获取响应内容:"
        RESPONSE=$(curl -s "$TEST_URL" 2>/dev/null || echo "无法连接")
        echo "$RESPONSE"
    else
        echo "✅ 访问成功"
    fi
else
    echo "⚠️  curl命令不可用，无法测试"
fi

echo ""

# 5. 检查PHP处理
echo "🐘 检查PHP处理..."
if [ -f "/data/www/api.ios.xxyx.cn/public/index.php" ]; then
    echo "✅ API入口文件存在"
    
    # 检查index.php中的路由处理
    if grep -q "接口不存在" "/data/www/api.ios.xxyx.cn/public/index.php"; then
        echo "✅ 找到'接口不存在'错误消息来源"
        echo "📋 相关代码:"
        grep -A 3 -B 3 "接口不存在" "/data/www/api.ios.xxyx.cn/public/index.php"
    else
        echo "⚠️  未在index.php中找到'接口不存在'消息"
    fi
else
    echo "❌ API入口文件不存在"
fi

echo ""

# 6. 分析问题
echo "🎯 问题分析..."
echo "================================"

if [ -f "$TARGET_FILE" ]; then
    if systemctl is-active --quiet caddy; then
        if grep -q "/storage/" "$CADDY_CONFIG" 2>/dev/null; then
            echo "🔍 可能的问题："
            echo "1. Caddy配置中的try_files指令拦截了/storage/*路径"
            echo "2. 路由优先级问题，API路由处理优先于静态文件"
            echo "3. handle/route指令配置不当"
            echo ""
            echo "💡 建议解决方案："
            echo "1. 运行修复脚本: sudo ./scripts/fix-storage-access-issue.sh"
            echo "2. 或手动更新Caddy配置，使用handle指令分离处理逻辑"
        else
            echo "❌ 主要问题：Caddy配置中缺少storage路径处理"
            echo "💡 解决方案：添加storage路径的file_server配置"
        fi
    else
        echo "❌ 主要问题：Caddy服务未运行"
        echo "💡 解决方案：启动Caddy服务"
    fi
else
    echo "❌ 主要问题：目标文件不存在"
    echo "💡 解决方案：检查文件上传和存储逻辑"
fi

echo ""
echo "🔧 快速修复命令："
echo "sudo ./scripts/fix-storage-access-issue.sh"

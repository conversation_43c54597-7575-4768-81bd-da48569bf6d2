#!/bin/bash

# 设置存储目录符号链接脚本
# 使存储目录可以通过Web访问

set -e

echo "🔗 设置存储目录符号链接..."

# 创建存储目录
echo "📁 创建存储目录..."
mkdir -p /data/storage/uploads/icons
mkdir -p /data/storage/uploads/ipa
mkdir -p /data/storage/resigned_ipas

# 设置权限
echo "🔐 设置目录权限..."
chown -R caddy:caddy /data/storage
chmod -R 755 /data/storage

# 在前端网站根目录创建storage符号链接
echo "🔗 创建符号链接..."
if [ ! -L "/data/www/ios.xxyx.cn/storage" ]; then
    ln -sf /data/storage /data/www/ios.xxyx.cn/storage
    echo "✅ 前端storage符号链接创建成功"
else
    echo "✅ 前端storage符号链接已存在"
fi

# 在API网站public目录创建storage符号链接
if [ ! -L "/data/www/api.ios.xxyx.cn/public/storage" ]; then
    ln -sf /data/storage /data/www/api.ios.xxyx.cn/public/storage
    echo "✅ API storage符号链接创建成功"
else
    echo "✅ API storage符号链接已存在"
fi

# 验证符号链接
echo "🔍 验证符号链接..."
if [ -L "/data/www/ios.xxyx.cn/storage" ] && [ -d "/data/www/ios.xxyx.cn/storage/uploads/icons" ]; then
    echo "✅ 前端storage链接验证成功"
else
    echo "❌ 前端storage链接验证失败"
fi

if [ -L "/data/www/api.ios.xxyx.cn/public/storage" ] && [ -d "/data/www/api.ios.xxyx.cn/public/storage/uploads/icons" ]; then
    echo "✅ API storage链接验证成功"
else
    echo "❌ API storage链接验证失败"
fi

# 创建测试图标文件
echo "🧪 创建测试图标文件..."
echo "test icon" > /data/storage/uploads/icons/test.png
chown caddy:caddy /data/storage/uploads/icons/test.png
chmod 644 /data/storage/uploads/icons/test.png

echo ""
echo "🎉 存储目录符号链接设置完成！"
echo ""
echo "📋 访问测试："
echo "- 前端图标访问: https://ios.xxyx.cn/storage/uploads/icons/test.png"
echo "- API图标访问: https://api.ios.xxyx.cn/storage/uploads/icons/test.png"
echo ""
echo "📁 目录结构："
echo "- 实际存储: /data/storage/uploads/icons/"
echo "- 前端链接: /data/www/ios.xxyx.cn/storage -> /data/storage"
echo "- API链接: /data/www/api.ios.xxyx.cn/public/storage -> /data/storage"
echo ""
echo "⚠️  注意事项："
echo "1. 确保Caddy服务有权限访问符号链接"
echo "2. 图标URL应该使用: https://ios.xxyx.cn/storage/uploads/icons/filename.png"
echo "3. 如果访问失败，检查Caddy配置中的follow_symlinks设置"

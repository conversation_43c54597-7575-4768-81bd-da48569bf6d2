# 存储权限问题修复指南

## 问题描述

上传队列服务出现权限错误：
```
PHP Warning: copy(/data/storage/uploads/ipa/xxx.ipa): Failed to open stream: Permission denied
```

## 问题原因

1. **存储目录不存在**：`/data/storage/uploads/ipa/` 目录未创建
2. **权限不足**：PHP进程无法写入存储目录
3. **用户组不匹配**：Web服务器用户与PHP进程用户权限不一致
4. **目录权限设置错误**：目录权限过于严格

## 解决方案

### 🚀 自动修复（推荐）

运行自动修复脚本：

```bash
# 给脚本添加执行权限
chmod +x scripts/fix-storage-permissions.sh

# 运行修复脚本（需要root权限）
sudo ./scripts/fix-storage-permissions.sh
```

### 🔍 检查权限状态

运行权限检查脚本：

```bash
# 检查当前权限状态
php scripts/check-permissions.php
```

### 🔧 手动修复步骤

如果自动修复失败，可以手动执行以下步骤：

#### 1. 创建存储目录

```bash
sudo mkdir -p /data/storage/uploads/icons
sudo mkdir -p /data/storage/uploads/ipa
sudo mkdir -p /data/storage/resigned_ipas
sudo mkdir -p /data/storage/temp
sudo mkdir -p /tmp/ipa_uploads
```

#### 2. 设置正确的权限

```bash
# 设置所有者为Web服务器用户
sudo chown -R caddy:caddy /data/storage
sudo chown -R caddy:caddy /tmp/ipa_uploads

# 设置目录权限
sudo chmod -R 755 /data/storage
sudo chmod -R 775 /data/storage/uploads
sudo chmod -R 775 /data/storage/temp
sudo chmod -R 775 /data/storage/resigned_ipas
sudo chmod -R 775 /tmp/ipa_uploads
```

#### 3. 检查PHP-FPM用户配置

```bash
# 查看PHP-FPM用户配置
grep -E "^(user|group)" /etc/php/8.3/fpm/pool.d/www.conf

# 如果PHP用户与Web用户不同，将PHP用户添加到Web用户组
sudo usermod -a -G caddy www-data
```

#### 4. 更新环境变量配置

编辑 `/data/www/api.ios.xxyx.cn/.env` 文件，确保包含：

```env
IPA_STORAGE_PATH=/data/storage/uploads/ipa
ICON_STORAGE_PATH=/data/storage/uploads/icons
```

#### 5. 重启服务

```bash
sudo systemctl restart php8.3-fpm
sudo systemctl restart caddy
sudo systemctl restart xios-queue  # 如果存在
```

## 验证修复效果

### 1. 运行权限检查

```bash
php scripts/check-permissions.php
```

### 2. 测试文件创建

```bash
# 测试在IPA目录创建文件
sudo -u caddy touch /data/storage/uploads/ipa/test.txt
ls -la /data/storage/uploads/ipa/test.txt
sudo rm /data/storage/uploads/ipa/test.txt
```

### 3. 测试上传功能

1. 通过前端界面上传一个IPA文件
2. 检查文件是否成功保存到 `/data/storage/uploads/ipa/`
3. 查看PHP错误日志确认无权限错误

### 4. 查看日志

```bash
# 查看PHP错误日志
tail -f /data/logs/php/error.log

# 查看队列处理日志
journalctl -u xios-queue -f

# 查看Web服务器日志
tail -f /data/logs/access/api.ios.xxyx.cn.log
```

## 目录结构和权限

修复后的目录结构和权限：

```
/data/storage/                    # 755 caddy:caddy
├── uploads/                      # 775 caddy:caddy
│   ├── icons/                   # 775 caddy:caddy
│   └── ipa/                     # 775 caddy:caddy
├── resigned_ipas/               # 775 caddy:caddy
└── temp/                        # 775 caddy:caddy

/tmp/ipa_uploads/                # 775 caddy:caddy
```

## 常见问题

### 1. 权限仍然被拒绝

**可能原因**：SELinux启用并阻止访问

**解决方案**：
```bash
# 检查SELinux状态
sestatus

# 如果启用，设置SELinux上下文
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P httpd_unified 1
sudo restorecon -R /data/storage
```

### 2. 磁盘空间不足

**检查磁盘空间**：
```bash
df -h /data
```

**清理空间**：
```bash
# 清理旧的临时文件
sudo find /tmp -name "ipa_*" -mtime +1 -delete
sudo find /data/storage/temp -mtime +1 -delete
```

### 3. PHP进程用户不匹配

**检查PHP-FPM配置**：
```bash
ps aux | grep php-fpm
```

**修改PHP-FPM用户**：
编辑 `/etc/php/8.3/fpm/pool.d/www.conf`：
```ini
user = caddy
group = caddy
```

然后重启PHP-FPM：
```bash
sudo systemctl restart php8.3-fpm
```

### 4. 环境变量未生效

**重新加载环境变量**：
```bash
# 重启PHP-FPM以重新加载.env文件
sudo systemctl restart php8.3-fpm
```

## 预防措施

1. **定期检查权限**：
   ```bash
   # 添加到crontab，每天检查一次
   0 2 * * * php /data/www/api.ios.xxyx.cn/scripts/check-permissions.php
   ```

2. **监控磁盘空间**：
   ```bash
   # 监控磁盘使用率
   df -h /data | awk 'NR==2 {print $5}' | sed 's/%//'
   ```

3. **日志轮转**：
   确保日志文件不会占用过多空间

## 部署脚本更新

已更新 `deploy.sh` 脚本，在部署时自动修复存储目录权限：

```bash
# 修复存储目录权限
echo "🔧 修复存储目录权限..."
mkdir -p /data/storage/uploads/icons
mkdir -p /data/storage/uploads/ipa
mkdir -p /data/storage/resigned_ipas
mkdir -p /data/storage/temp

# 设置正确的权限和所有者
chown -R caddy:caddy /data/storage
chmod -R 755 /data/storage

# 确保PHP进程可以写入
chmod -R 775 /data/storage/uploads
chmod -R 775 /data/storage/temp

echo "✅ 存储目录权限修复完成"
```

## 总结

通过以上修复步骤，应该能够解决存储权限问题：

1. ✅ **目录创建**：确保所有必要的存储目录存在
2. ✅ **权限设置**：设置正确的文件和目录权限
3. ✅ **用户组配置**：确保PHP进程有权限访问存储目录
4. ✅ **环境变量**：配置正确的存储路径
5. ✅ **服务重启**：重启相关服务使配置生效

现在上传和重签功能应该可以正常工作，不再出现权限错误！
